#!/usr/bin/env python3
"""
测试版本的三角套利扫描器，使用模拟数据验证逻辑
"""

import json
import math
from collections import defaultdict

# 模拟数据
QUOTE_WHITELIST = {"USDT", "BTC", "ETH", "BNB", "FDUSD", "USDC"}

# 模拟现货交易对数据
mock_spot_symbols = [
    {"symbol": "BTCUSDT", "baseAsset": "BTC", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "ETHUSDT", "baseAsset": "ETH", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "BNBUSDT", "baseAsset": "BNB", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "BTCETH", "baseAsset": "BTC", "quoteAsset": "ETH", "status": "TRADING"},
    {"symbol": "BNBBTC", "baseAsset": "BNB", "quoteAsset": "BTC", "status": "TRADING"},
    {"symbol": "BNBETH", "baseAsset": "BNB", "quoteAsset": "ETH", "status": "TRADING"},
    {"symbol": "BTCUSDC", "baseAsset": "BTC", "quoteAsset": "USDC", "status": "TRADING"},
    {"symbol": "ETHUSDC", "baseAsset": "ETH", "quoteAsset": "USDC", "status": "TRADING"},
    {"symbol": "BNBUSDC", "baseAsset": "BNB", "quoteAsset": "USDC", "status": "TRADING"},
    {"symbol": "USDCUSDT", "baseAsset": "USDC", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "ADAUSDT", "baseAsset": "ADA", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "ADABTC", "baseAsset": "ADA", "quoteAsset": "BTC", "status": "TRADING"},
    {"symbol": "SOLUSDT", "baseAsset": "SOL", "quoteAsset": "USDT", "status": "TRADING"},
    {"symbol": "SOLBTC", "baseAsset": "SOL", "quoteAsset": "BTC", "status": "TRADING"},
    {"symbol": "SOLETH", "baseAsset": "SOL", "quoteAsset": "ETH", "status": "TRADING"},
]

# 模拟期货交易对数据（永续合约）
mock_futures_symbols = [
    {"symbol": "BTCUSDT", "baseAsset": "BTC", "quoteAsset": "USDT", "status": "TRADING", "contractType": "PERPETUAL"},
    {"symbol": "ETHUSDT", "baseAsset": "ETH", "quoteAsset": "USDT", "status": "TRADING", "contractType": "PERPETUAL"},
    {"symbol": "BNBUSDT", "baseAsset": "BNB", "quoteAsset": "USDT", "status": "TRADING", "contractType": "PERPETUAL"},
    {"symbol": "ADAUSDT", "baseAsset": "ADA", "quoteAsset": "USDT", "status": "TRADING", "contractType": "PERPETUAL"},
    {"symbol": "SOLUSDT", "baseAsset": "SOL", "quoteAsset": "USDT", "status": "TRADING", "contractType": "PERPETUAL"},
]

# 模拟现货交易量数据
mock_spot_volumes = {
    "BTCUSDT": 1000000000,  # 10亿
    "ETHUSDT": 800000000,   # 8亿
    "BNBUSDT": 200000000,   # 2亿
    "BTCETH": 50000000,     # 5千万
    "BNBBTC": 30000000,     # 3千万
    "BNBETH": 25000000,     # 2.5千万
    "BTCUSDC": 100000000,   # 1亿
    "ETHUSDC": 80000000,    # 8千万
    "BNBUSDC": 20000000,    # 2千万
    "USDCUSDT": 150000000,  # 1.5亿
    "ADAUSDT": 50000000,    # 5千万
    "ADABTC": 10000000,     # 1千万
    "SOLUSDT": 300000000,   # 3亿
    "SOLBTC": 40000000,     # 4千万
    "SOLETH": 30000000,     # 3千万
}

# 模拟期货交易量数据
mock_futures_volumes = {
    "BTC": 2000000000,  # 20亿
    "ETH": 1500000000,  # 15亿
    "BNB": 400000000,   # 4亿
    "ADA": 100000000,   # 1亿
    "SOL": 600000000,   # 6亿
}

def analyze_trading_pairs():
    """分析交易对，按照新规则筛选"""
    print("=== 三角套利交易对分析（基于新规则） ===")
    print("规则1: 既有现货交易，又有期货交易")
    print("规则2: 现货交易量要从大到小筛选")
    print("规则3: 同一个币种的quote asset种类越多越好\n")
    
    # 提取期货base assets
    futures_symbols = set()
    for s in mock_futures_symbols:
        if s["status"] == "TRADING" and s["contractType"] == "PERPETUAL":
            futures_symbols.add(s["baseAsset"])
    
    print(f"期货base assets: {sorted(futures_symbols)}")
    
    # 筛选既有现货又有期货的币种
    base_to_quotes = defaultdict(set)
    sym_name = {}
    
    for s in mock_spot_symbols:
        if s["status"] != "TRADING":
            continue
        b, q = s["baseAsset"], s["quoteAsset"]
        # 规则1：只保留既有现货又有期货的币种
        if b in futures_symbols:
            base_to_quotes[b].add(q)
            sym_name[(b, q)] = s["symbol"]
    
    print(f"既有现货又有期货的币种: {sorted(base_to_quotes.keys())}")
    
    # 分析每个币种
    candidates = []
    for base, quotes in base_to_quotes.items():
        usable_quotes = quotes & QUOTE_WHITELIST
        if len(usable_quotes) < 2:
            continue
            
        # 规则3：quote asset多样性
        quote_diversity = len(usable_quotes)
        
        # 计算可组成的三角套利对数
        tri_pairs = []
        ulist = sorted(list(usable_quotes))
        for i in range(len(ulist)):
            for j in range(i + 1, len(ulist)):
                q1, q2 = ulist[i], ulist[j]
                # 检查是否存在交叉市场
                cross_pair = None
                if (q1, q2) in sym_name:
                    cross_pair = sym_name[(q1, q2)]
                elif (q2, q1) in sym_name:
                    cross_pair = sym_name[(q2, q1)]
                
                if cross_pair:
                    tri_pairs.append((q1, q2, cross_pair))
        
        tri_count = len(tri_pairs)
        if tri_count == 0:
            continue
            
        # 规则2：现货总交易量
        spot_vol_sum = sum(mock_spot_volumes.get(sym_name.get((base, q), ""), 0) for q in usable_quotes)
        
        # 期货交易量因子
        futures_vol = mock_futures_volumes.get(base, 0)
        futures_factor = math.log10(futures_vol + 1) if futures_vol > 0 else 0
        
        # 综合评分
        quote_diversity_factor = quote_diversity / len(QUOTE_WHITELIST)
        score = (quote_diversity_factor * 
                math.log10(spot_vol_sum + 1) * 
                (1 + futures_factor * 0.2))
        
        candidates.append({
            "base": base,
            "quote_diversity": quote_diversity,
            "usable_quotes": sorted(usable_quotes),
            "tri_count": tri_count,
            "tri_pairs": tri_pairs,
            "spot_vol_sum": spot_vol_sum,
            "futures_vol": futures_vol,
            "score": score
        })
    
    # 按评分排序
    candidates.sort(key=lambda x: x["score"], reverse=True)
    
    # 输出结果
    print(f"\n=== 分析结果 ===")
    print(f"{'Base':<6} {'QDiv':<4} {'TriCnt':<6} {'SpotVolM':<10} {'FutVolM':<9} {'Score':<8} {'Quotes'}")
    print("-" * 70)
    
    for c in candidates:
        print(f"{c['base']:<6} {c['quote_diversity']:<4} {c['tri_count']:<6} "
              f"{c['spot_vol_sum']/1e6:<10.1f} {c['futures_vol']/1e6:<9.1f} "
              f"{c['score']:<8.2f} {','.join(c['usable_quotes'])}")
        
        # 显示三角套利对
        for i, (q1, q2, cross) in enumerate(c['tri_pairs']):
            leg1 = sym_name.get((c['base'], q1), f"{c['base']}{q1}")
            leg2 = sym_name.get((c['base'], q2), f"{c['base']}{q2}")
            print(f"      Triangle {i+1}: {leg1} ↔ {leg2} ↔ {cross}")
    
    print(f"\n=== 总结 ===")
    print(f"总现货交易对: {len(mock_spot_symbols)}")
    print(f"期货base assets: {len(futures_symbols)}")
    print(f"既有现货又有期货: {len(base_to_quotes)}")
    print(f"符合条件的候选币种: {len(candidates)}")
    
    # 保存结果到JSON文件
    with open("scripts/tri-arb-analysis-result.json", "w") as f:
        json.dump(candidates, f, indent=2, default=str)
    print(f"详细结果已保存到 scripts/tri-arb-analysis-result.json")

if __name__ == "__main__":
    analyze_trading_pairs()
