use std::mem;

use libc::{
    CPU_SET, CPU_ZERO, SCHED_FIFO, cpu_set_t, pid_t, sched_param, sched_setaffinity,
    sched_setscheduler,
};

use crate::{info_unsafe, warn_unsafe};

pub fn bind_to_core(core_id: usize) {
    unsafe {
        let mut set: cpu_set_t = mem::zeroed();
        CPU_ZERO(&mut set);
        CPU_SET(core_id, &mut set);

        let pid: pid_t = 0; // 0 表示当前线程

        let result = sched_setaffinity(pid, mem::size_of::<cpu_set_t>(), &set);

        if result != 0 {
            warn_unsafe!(
                "Failed to set CPU affinity: {}",
                std::io::Error::last_os_error()
            );
        } else {
            info_unsafe!("Successfully bound to core {}", core_id);
        }
        let param = sched_param { sched_priority: 99 };
        let pid = std::process::id() as i32;
        let ret = sched_setscheduler(pid, SCHED_FIFO, &param);
        if ret != 0 {
            warn_unsafe!(
                "Warning: Failed to set SCHED_FIFO: {}",
                std::io::Error::last_os_error()
            );
            warn_unsafe!("running as normal priority...");
        }
    }
}
