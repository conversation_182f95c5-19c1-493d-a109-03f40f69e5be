#![cfg(test)]

use crate::engine::gate_const::{
    CURRENCY_LEN, GATE_BASE_ASSETS, GATE_PRICE_TICKS, GateBaseAssetCurrency,
};

#[test]
fn test_gate_base_asset_currency_from_symbol() {
    // Test various symbol formats for KAITO
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("kaitousdt"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITOUSDT"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITO_USDT"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITO"),
        Some(GateBaseAssetCurrency::KAITO)
    );

    // Test other currencies
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("SUSHIUSDT"),
        Some(GateBaseAssetCurrency::SUSHI)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("USUALUSDT"),
        Some(GateBaseAssetCurrency::USUAL)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("THETAUSDT"),
        Some(GateBaseAssetCurrency::THETA)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("UMAUSDT"),
        Some(GateBaseAssetCurrency::UMA)
    );

    // Test invalid symbols
    assert_eq!(GateBaseAssetCurrency::from_symbol("KAITO_USDT_"), None);
    assert_eq!(GateBaseAssetCurrency::from_symbol("INVALID"), None);
    assert_eq!(GateBaseAssetCurrency::from_symbol(""), None);
}

#[test]
fn test_gate_base_asset_currency_to_str() {
    assert_eq!(GateBaseAssetCurrency::KAITO.to_str(), "KAITO");
    assert_eq!(GateBaseAssetCurrency::SUSHI.to_str(), "SUSHI");
    assert_eq!(GateBaseAssetCurrency::USUAL.to_str(), "USUAL");
    assert_eq!(GateBaseAssetCurrency::THETA.to_str(), "THETA");
    assert_eq!(GateBaseAssetCurrency::UMA.to_str(), "UMA");
}

#[test]
fn test_gate_base_asset_currency_to_bn_symbol() {
    assert_eq!(GateBaseAssetCurrency::KAITO.to_bn_symbol(), "KAITOUSDT");
    assert_eq!(GateBaseAssetCurrency::SUSHI.to_bn_symbol(), "SUSHIUSDT");
    assert_eq!(GateBaseAssetCurrency::USUAL.to_bn_symbol(), "USUALUSDT");
    assert_eq!(GateBaseAssetCurrency::THETA.to_bn_symbol(), "THETAUSDT");
    assert_eq!(GateBaseAssetCurrency::UMA.to_bn_symbol(), "UMAUSDT");
}

#[test]
fn test_gate_base_asset_currency_to_gate_symbol() {
    assert_eq!(GateBaseAssetCurrency::KAITO.to_gate_symbol(), "KAITO_USDT");
    assert_eq!(GateBaseAssetCurrency::SUSHI.to_gate_symbol(), "SUSHI_USDT");
    assert_eq!(GateBaseAssetCurrency::USUAL.to_gate_symbol(), "USUAL_USDT");
    assert_eq!(GateBaseAssetCurrency::THETA.to_gate_symbol(), "THETA_USDT");
    assert_eq!(GateBaseAssetCurrency::UMA.to_gate_symbol(), "UMA_USDT");
}

#[test]
fn test_gate_base_asset_currency_from_usize() {
    assert_eq!(
        GateBaseAssetCurrency::from_usize(0),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_usize(1),
        Some(GateBaseAssetCurrency::SUSHI)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_usize(2),
        Some(GateBaseAssetCurrency::USUAL)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_usize(3),
        Some(GateBaseAssetCurrency::THETA)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_usize(4),
        Some(GateBaseAssetCurrency::UMA)
    );
    assert_eq!(GateBaseAssetCurrency::from_usize(5), None);
    assert_eq!(GateBaseAssetCurrency::from_usize(100), None);
}

#[test]
fn test_gate_base_assets_constants() {
    // Test that GATE_BASE_ASSETS contains expected values
    assert_eq!(GATE_BASE_ASSETS.len(), CURRENCY_LEN);
    assert!(GATE_BASE_ASSETS.contains(&"KAITO"));
    assert!(GATE_BASE_ASSETS.contains(&"SUSHI"));
    assert!(GATE_BASE_ASSETS.contains(&"USUAL"));
    assert!(GATE_BASE_ASSETS.contains(&"THETA"));
    assert!(GATE_BASE_ASSETS.contains(&"UMA"));
}

#[test]
fn test_gate_price_ticks() {
    // Test that GATE_PRICE_TICKS has correct length and contains valid values
    assert_eq!(GATE_PRICE_TICKS.len(), CURRENCY_LEN);

    // All price ticks should be positive
    for &tick in GATE_PRICE_TICKS.iter() {
        assert!(tick > 0.0, "Price tick should be positive: {}", tick);
    }

    // Test specific known values based on the JSON file
    assert_eq!(GATE_PRICE_TICKS[0], 0.0001); // KAITO
    assert_eq!(GATE_PRICE_TICKS[1], 0.0001); // SUSHI
    assert_eq!(GATE_PRICE_TICKS[2], 0.00001); // USUAL
    assert_eq!(GATE_PRICE_TICKS[3], 0.0001); // THETA
    assert_eq!(GATE_PRICE_TICKS[4], 0.0001); // UMA
}
