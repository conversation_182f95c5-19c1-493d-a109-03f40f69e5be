use std::collections::HashMap;

use crate::{
    encoding::{
        futures_order::OrderSide,
        futures_order_response::{OrderStatus, OrderTradeUpdate},
    },
    engine::{
        arbitrage_engine_futures_utils::generate_take_profit_and_stop_loss_opportunity,
        futures_const::{FUTURES_PRICE_TICK_SIZE, FUTURES_QUANTITY_TICK_SIZE},
        futures_position::FuturesPosition,
    },
    utils::perf::{circles_to_ns, system_now_in_us},
};

#[derive(PartialEq, Eq, PartialOrd, Ord, Clone, Copy, Debug)]
pub enum OpportunityStatus {
    Pending,
    Filled,
    Success,
    Fail,
    Canceled,
}

#[derive(PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, Co<PERSON>, Debug)]
pub enum PredictResult {
    None,
    Win,
    Loss,
}

#[derive(PartialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Debug)]
pub enum OpportunityType {
    LargeTrade,
    OrderbookImbalance,
    TakeProfit,
    StopLoss,
}

impl OpportunityType {
    pub fn should_cal_win_or_loss(&self) -> bool {
        match self {
            OpportunityType::LargeTrade => true,
            OpportunityType::OrderbookImbalance => true,
            OpportunityType::TakeProfit => false,
            OpportunityType::StopLoss => false,
        }
    }
}

#[derive(Clone)]
pub struct Opportunity {
    pub id: u64,
    pub origin_order_id: u64,
    pub price: f64,
    pub stop_price: f64,
    pub side: OrderSide,
    pub status: OpportunityStatus,
    pub predict_result: PredictResult,
    pub opportunity_type: OpportunityType,
}

pub struct OpportunitySimulator {
    pub opportunities: HashMap<u64, Opportunity>,
    pub take_profit_opportunities: HashMap<u64, [Opportunity; 2]>,
    pub total_count: usize,
    pub pending_count: usize,
    pub cancel_count: usize,
    pub filled_count: usize,
    pub win_success_count: usize,
    pub loss_success_count: usize,
    pub win_failed_count: usize,
    pub loss_failed_count: usize,
    pub succuess_count: usize, // take profit订单成交
    pub failed_count: usize,   // stop loss订单成交
    pub win_count: usize,      // 预测成功
    pub loss_count: usize,     // 预测失败
    pub win_count_large_trade: usize,
    pub win_count_orderbook_imbalance: usize,
    pub loss_count_large_trade: usize,
    pub loss_count_orderbook_imbalance: usize,
    pub positions: FuturesPosition,
    pub avg_fill_latency: f64,
    pub avg_win_latency: f64,
    pub avg_loss_latency: f64,
    pub avg_predict_win_tick_count: f64,
    pub avg_predict_loss_tick_count: f64,
    pub last_log_time: u64,
}

impl OpportunitySimulator {
    pub fn new() -> Self {
        Self {
            opportunities: HashMap::new(),
            take_profit_opportunities: HashMap::new(),
            total_count: 0,
            pending_count: 0,
            succuess_count: 0,
            failed_count: 0,
            cancel_count: 0,
            win_count: 0,
            loss_count: 0,
            win_count_large_trade: 0,
            win_count_orderbook_imbalance: 0,
            loss_count_large_trade: 0,
            loss_count_orderbook_imbalance: 0,
            positions: FuturesPosition::new(),
            filled_count: 0,
            avg_fill_latency: 0.0,
            avg_win_latency: 0.0,
            avg_loss_latency: 0.0,
            avg_predict_win_tick_count: 0.0,
            avg_predict_loss_tick_count: 0.0,
            win_success_count: 0,
            loss_success_count: 0,
            win_failed_count: 0,
            loss_failed_count: 0,
            last_log_time: system_now_in_us(),
        }
    }

    pub fn add_opportunity(&mut self, opportunity: Opportunity) {
        self.opportunities.insert(opportunity.id, opportunity);
        self.total_count += 1;
        self.pending_count += 1;
    }

    pub fn add_take_profit_and_stop_loss_opportunity(&mut self, opportunity: &[Opportunity; 2]) {
        self.take_profit_opportunities
            .insert(opportunity[0].id, opportunity.clone());
    }

    // pub fn add_trade(&mut self, trade: &OrderTradeUpdate) {
    //     let order_id = trade.order_id;
    //     if self.opportunities.contains_key(&order_id) {
    //         self.opportunities.get_mut(&order_id).unwrap().status = OpportunityStatus::Filled;
    //     } else if self.take_profit_opportunities.contains_key(&order_id) {
    //         self.take_profit_opportunities
    //             .get_mut(&order_id)
    //             .unwrap()
    //             .status = OpportunityStatus::Filled;
    //         self.pending_count -= 1;
    //         self.succuess_count += 1;
    //     }
    // }

    fn can_fill(opportunity: &Opportunity, best_bid: f64, best_ask: f64) -> bool {
        (opportunity.side == OrderSide::Buy && opportunity.price >= best_ask)
            || (opportunity.side == OrderSide::Sell && opportunity.price <= best_bid)
    }

    pub fn update_price(&mut self, best_bid: f64, best_ask: f64) {
        let mut has_filled = false;
        let now = system_now_in_us();

        // Collect data for stats updates and filled opportunities
        let mut filled_opportunities = Vec::new();

        for opportunity in self.opportunities.iter_mut() {
            // Collect data for stats update
            if opportunity.1.predict_result == PredictResult::None
                && opportunity.1.opportunity_type.should_cal_win_or_loss()
                && circles_to_ns(now - opportunity.0) > 2_000_000.0
            {
                match opportunity.1.side {
                    OrderSide::Buy => match opportunity.1.price < best_bid {
                        true => {
                            let tick_count = ((best_bid - opportunity.1.price)
                                / FUTURES_PRICE_TICK_SIZE)
                                as usize;
                            self.avg_predict_win_tick_count = (self.avg_predict_win_tick_count
                                * self.win_count as f64
                                + tick_count as f64)
                                / (self.win_count + 1) as f64;
                            self.win_count += 1;
                            opportunity.1.predict_result = PredictResult::Win;
                            if opportunity.1.status == OpportunityStatus::Success {
                                self.win_success_count += 1;
                            } else if opportunity.1.status == OpportunityStatus::Fail {
                                self.win_failed_count += 1;
                            }
                        }
                        false => {
                            let tick_count = ((opportunity.1.price - best_bid)
                                / FUTURES_PRICE_TICK_SIZE)
                                as usize;
                            self.avg_predict_loss_tick_count = (self.avg_predict_loss_tick_count
                                * self.loss_count as f64
                                + tick_count as f64)
                                / (self.loss_count + 1) as f64;
                            opportunity.1.predict_result = PredictResult::Loss;
                            self.loss_count += 1;
                            if opportunity.1.status == OpportunityStatus::Success {
                                self.loss_success_count += 1;
                            } else if opportunity.1.status == OpportunityStatus::Fail {
                                self.loss_failed_count += 1;
                            }
                        }
                    },
                    OrderSide::Sell => match opportunity.1.price > best_ask {
                        true => {
                            let tick_count = ((opportunity.1.price - best_ask)
                                / FUTURES_PRICE_TICK_SIZE)
                                as usize;
                            self.avg_predict_win_tick_count = (self.avg_predict_win_tick_count
                                * self.win_count as f64
                                + tick_count as f64)
                                / (self.win_count + 1) as f64;
                            self.win_count += 1;
                            opportunity.1.predict_result = PredictResult::Win;
                            if opportunity.1.status == OpportunityStatus::Success {
                                self.win_success_count += 1;
                            } else if opportunity.1.status == OpportunityStatus::Fail {
                                self.win_failed_count += 1;
                            }
                        }
                        false => {
                            let tick_count = ((best_ask - opportunity.1.price)
                                / FUTURES_PRICE_TICK_SIZE)
                                as usize;
                            self.avg_predict_loss_tick_count = (self.avg_predict_loss_tick_count
                                * self.loss_count as f64
                                + tick_count as f64)
                                / (self.loss_count + 1) as f64;
                            opportunity.1.predict_result = PredictResult::Loss;
                            self.loss_count += 1;
                            if opportunity.1.status == OpportunityStatus::Success {
                                self.loss_success_count += 1;
                            } else if opportunity.1.status == OpportunityStatus::Fail {
                                self.loss_failed_count += 1;
                            }
                        }
                    },
                };
            }

            if opportunity.1.status == OpportunityStatus::Pending && (now - opportunity.0) > 3_000 {
                if (now - opportunity.0) > 1000 * 100 {
                    opportunity.1.status = OpportunityStatus::Canceled;
                    self.cancel_count += 1;
                    self.pending_count -= 1;
                } else if Self::can_fill(opportunity.1, best_bid, best_ask) {
                    let price = opportunity.1.price;
                    let side = opportunity.1.side;
                    let order_id = opportunity.1.id;
                    let opportunity_time = *opportunity.0;
                    opportunity.1.status = OpportunityStatus::Filled;
                    filled_opportunities.push((price, side, order_id, opportunity_time));
                    has_filled = true;
                }
            }
        }

        // Process filled opportunities
        for (price, side, order_id, opportunity_time) in filled_opportunities {
            self.avg_fill_latency = ((now - opportunity_time) as f64
                + (self.filled_count as f64 * self.avg_fill_latency))
                / (self.filled_count + 1) as f64;
            self.filled_count += 1;

            let opportunities =
                generate_take_profit_and_stop_loss_opportunity(price, side, opportunity_time);
            self.take_profit_opportunities
                .insert(opportunities[0].id, opportunities);

            let trade = OrderTradeUpdate {
                order_id,
                order_status: OrderStatus::Filled,
                order_side: side,
                price,
                quantity: FUTURES_QUANTITY_TICK_SIZE,
                avg_price: 0.0,
            };
            self.positions.add_trade(&trade);
        }
        for opportunity in self.take_profit_opportunities.iter_mut() {
            if opportunity.1[0].status == OpportunityStatus::Pending {
                let (filled_index, canceled_index) =
                    if Self::can_fill(&opportunity.1[0], best_bid, best_ask) {
                        (0, 1)
                    } else if Self::can_fill(&opportunity.1[1], best_bid, best_ask) {
                        (1, 0)
                    } else {
                        continue;
                    };
                let origin_opportunity = self
                    .opportunities
                    .get_mut(&opportunity.1[filled_index].origin_order_id)
                    .unwrap();
                crate::info!(
                    r#"
Match take profit result: {}
current price: {}
take profit price: {} {}
stop loss price: {} {}
origin price: {} {}
"#,
                    filled_index,
                    (best_bid + best_ask) / 2.0,
                    opportunity.1[0].side.to_string(),
                    opportunity.1[0].price,
                    opportunity.1[1].side.to_string(),
                    opportunity.1[1].price,
                    origin_opportunity.side.to_string(),
                    origin_opportunity.price
                );
                opportunity.1[filled_index].status = OpportunityStatus::Success;
                opportunity.1[canceled_index].status = OpportunityStatus::Canceled;
                self.pending_count -= 1;

                if filled_index == 0 {
                    self.avg_win_latency = (self.avg_win_latency * self.succuess_count as f64
                        + (now - origin_opportunity.id) as f64)
                        / (self.succuess_count + 1) as f64;
                    self.succuess_count += 1;
                    origin_opportunity.status = OpportunityStatus::Success;
                    if origin_opportunity.predict_result == PredictResult::Win {
                        self.win_success_count += 1;
                    } else if origin_opportunity.predict_result == PredictResult::Loss {
                        self.loss_success_count += 1;
                    }
                } else {
                    self.avg_loss_latency = (self.avg_loss_latency * self.loss_count as f64
                        + (now - origin_opportunity.id) as f64)
                        / (self.loss_count + 1) as f64;
                    self.failed_count += 1;
                    origin_opportunity.status = OpportunityStatus::Fail;
                    if origin_opportunity.predict_result == PredictResult::Win {
                        self.win_failed_count += 1;
                    } else if origin_opportunity.predict_result == PredictResult::Loss {
                        self.loss_failed_count += 1;
                    }
                }
                let trade = OrderTradeUpdate {
                    order_id: opportunity.1[0].id,
                    order_status: OrderStatus::Filled,
                    order_side: opportunity.1[0].side,
                    price: opportunity.1[0].price,
                    quantity: FUTURES_QUANTITY_TICK_SIZE,
                    avg_price: 0.0,
                };
                self.positions.add_trade(&trade);
                has_filled = true;
            }
        }
        self.positions.update_current_price(best_bid, best_ask);
        if has_filled {
            self.log_stats();
        }
    }

    pub fn increase_pending_count(&mut self) {
        self.pending_count += 1;
        self.total_count += 1;
        self.log_stats();
    }

    pub fn increase_cancel_count(&mut self) {
        self.cancel_count += 1;
        self.pending_count -= 1;
        self.log_stats();
    }

    pub fn increase_success_count(&mut self) {
        self.succuess_count += 1;
        self.log_stats();
    }

    pub fn increase_fail_count(&mut self) {
        self.failed_count += 1;
        self.log_stats();
    }

    pub fn increase_filled_count(&mut self) {
        self.filled_count += 1;
        self.pending_count -= 1;
        self.log_stats();
    }

    fn log_stats(&mut self) {
        if (system_now_in_us() - self.last_log_time) > 1000 * 1000 * 5 {
            self.last_log_time = system_now_in_us();
        } else {
            return;
        }
        crate::info!(
            r#"
Total_count: {}
Pending_count: {}
Filled_count: {}
Cancel_count: {}
Success_count: {}
Fail_count: {}
Avg_success_lateny: {:.2}ms
Avg_fail_latency: {:.2}ms
Win_success_count: {}
Win_failed_count: {}
Loss_success_count: {}
Loss_failed_count: {}
Win_count: {}
Loss_count: {}
Win_rate: {:.2}%
Avg_win_tick_count: {:.1}
Avg_loss_tick_count: {:.1}
Avg_fill_latency: {:.2}ms
Realized pnl: {:.5}
Unrealized pnl: {:.5}
Success_rate: {:.3}%
Current position: {:.3}
Entry price: {:.2}
"#,
            self.total_count,
            self.pending_count,
            self.filled_count,
            self.cancel_count,
            self.succuess_count,
            self.failed_count,
            self.avg_win_latency / 1000.0,
            self.avg_loss_latency / 1000.0,
            self.win_success_count,
            self.win_failed_count,
            self.loss_success_count,
            self.loss_failed_count,
            self.win_count,
            self.loss_count,
            (self.win_count as f64 / (self.win_count + self.loss_count) as f64) * 100.0,
            self.avg_predict_win_tick_count,
            self.avg_predict_loss_tick_count,
            self.avg_fill_latency / 1000.0,
            self.positions.realized_pnl,
            self.positions.unrealized_pnl,
            (self.succuess_count as f64 / self.total_count as f64) * 100.0,
            self.positions.position,
            self.positions.entry_price,
        );
    }
}
