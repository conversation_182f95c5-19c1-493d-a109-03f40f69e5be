use crate::{
    Currency, EdgeDirection, PREDEFINED_RINGS, TRADING_PAIR_RATES, TradingPair,
    engine::{
        arbitrage_engine::ORDER_PRICES,
        trading_pair::{CURRENCY_BALANCES, TRADING_PAIR_QTY},
    },
};

pub fn level_1_in_usdt(pair: TradingPair, dir: EdgeDirection) -> f64 {
    unsafe {
        let pair_index = pair as usize;
        let dir_index = dir as usize;
        let rate = TRADING_PAIR_RATES[pair_index][dir_index];
        let price = match dir {
            EdgeDirection::Forward => 1.0 / rate,
            EdgeDirection::Reverse => rate,
        };
        let qty = TRADING_PAIR_QTY[pair_index][dir_index];
        let amount = qty * price;
        match pair.quote() {
            Currency::XUSDT | Currency::XUSDC => amount,
            _ => {
                let value_pair = TradingPair::from((pair.quote(), Currency::XUSDT));
                let value_rate =
                    TRADING_PAIR_RATES[value_pair as usize][EdgeDirection::Reverse as usize];
                amount * value_rate
            }
        }
    }
}

pub fn balance_by_dir(pair: TradingPair, dir: EdgeDirection) -> f64 {
    unsafe {
        match dir {
            EdgeDirection::Forward => CURRENCY_BALANCES[pair.quote() as usize],
            EdgeDirection::Reverse => CURRENCY_BALANCES[pair.base() as usize],
        }
    }
}

pub fn record_order_price(ring_index: usize) {
    let mut i = 0;
    for pair in PREDEFINED_RINGS[ring_index] {
        unsafe {
            ORDER_PRICES[i] = match pair.1 {
                EdgeDirection::Forward => {
                    1.0 / TRADING_PAIR_RATES[pair.0 as usize][EdgeDirection::Forward as usize]
                }
                EdgeDirection::Reverse => {
                    TRADING_PAIR_RATES[pair.0 as usize][EdgeDirection::Reverse as usize]
                }
            };
        }
        i += 1;
    }
}

pub fn min_balance_in_ring(ring_index: usize) -> f64 {
    let mut min_balance = f64::MAX;
    for pair in PREDEFINED_RINGS[ring_index] {
        let balance = balance_by_dir(pair.0, pair.1);
        if balance < min_balance {
            min_balance = balance;
        }
    }
    min_balance
}

pub fn usdt_amount_to_base_quantity_by_dir(amount: f64, pair: TradingPair) -> f64 {
    let amount_in_quote = match pair.quote() {
        Currency::XUSDT | Currency::XUSDC => amount,
        _ => {
            let value_pair = TradingPair::from((pair.quote(), Currency::XUSDT));
            let value_rate =
                unsafe { TRADING_PAIR_RATES[value_pair as usize][EdgeDirection::Forward as usize] };
            amount * value_rate
        }
    };
    amount_in_quote * unsafe { TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}
