pub static GATE_BASE_ASSETS: &[&str] = &[
    "LDO",
    "FORM",
    "ZRX",
];
pub const CURRENCY_LEN: usize = 3;
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum GateBaseAssetCurrency {
    LDO,
    FORM,
    ZRX,
}
impl GateBaseAssetCurrency {
    pub fn to_str(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::LDO => "LDO",
            GateBaseAssetCurrency::FORM => "FORM",
            GateBaseAssetCurrency::ZRX => "ZRX",
        }
    }
    pub fn from_symbol(symbol: &str) -> Option<Self> {
        let upper_symbol = symbol.to_uppercase();
        let symbol = upper_symbol.trim_end_matches("USDT").trim_end_matches('_');
        match symbol {
            "LDO" => Some(GateBaseAssetCurrency::LDO),
            "FORM" => Some(GateBaseAssetCurrency::FORM),
            "ZRX" => Some(GateBaseAssetCurrency::ZRX),
            _ => None,
        }
    }
    pub fn to_bn_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::LDO => "LDOUSDT",
            GateBaseAssetCurrency::FORM => "FORMUSDT",
            GateBaseAssetCurrency::ZRX => "ZRXUSDT",
        }
    }
    pub fn to_gate_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::LDO => "LDO_USDT",
            GateBaseAssetCurrency::FORM => "FORM_USDT",
            GateBaseAssetCurrency::ZRX => "ZRX_USDT",
        }
    }
    pub fn from_usize(num: usize) -> Option<Self> {
        match num {
            0 => Some(GateBaseAssetCurrency::LDO),
            1 => Some(GateBaseAssetCurrency::FORM),
            2 => Some(GateBaseAssetCurrency::ZRX),
            _ => None,
        }
    }
}
// bn and gate bbo map
 //[
//(bn_best_bid_price, bn_best_bid_qty, bn_best_ask_price, bn_best_ask_qty), 
//(gate_best_bid_price, gate_best_bid_qty, gate_best_ask_price, gate_best_ask_qty)]
pub static mut BN_AND_GATE_BBO_MAP: [([f64; 4], [f64; 4]); 3] = [([0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]); 3];
use crate::encoding::gate;
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct GateMakerOrder {
    pub req_id: u64,
    pub order_id: u64,
    pub price: f64,
    pub status: gate::UnifiedOrderStatus,
}
// req id, order id, order price, order status(0 for pending, 1 for ack
pub static mut GATE_PENDING_ORDERS: [GateMakerOrder; 3] = [GateMakerOrder { req_id: 0, order_id: 0, price: 0.0, status: gate::UnifiedOrderStatus::Uninitialized }; 3];
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct BnHedgeOrder { pub qty: f64, pub order_id: u64 }
pub static mut BN_HEDGE_ORDERS: [BnHedgeOrder; 3] = [BnHedgeOrder { qty: 0.0, order_id: 0 }; 3];
pub static GATE_PRICE_TICKS: [f64; 3] = [
    0.0001f64, // LDO
    0.0001f64, // FORM
    0.0001f64, // ZRX
];
pub static GATE_QUANTO_MULTIPLIER: [f64; 3] = [
    1f64, // LDO
    1f64, // FORM
    1f64, // ZRX
];
pub static BN_PRICE_TICK: [f64; 3] = [
    0.0001f64, // LDO
    0.0001f64, // FORM
    0.0001f64, // ZRX
];

// 0 for bn avg price, 1 for bn qty, 2 for gate avg price, 3 for gate qty
pub static mut HEDGE_STATUS: [(f64, f64, f64, f64); 3] = [(0.0, 0.0, 0.0, 0.0); 3];
pub static GATE_LOT_SIZE: [f64; 3] = [
    1f64, // LDO
    1f64, // FORM
    1f64, // ZRX
];
pub static GATE_MIN_NATIONAL: [f64; 3] = [
    1.183f64, // LDO
    3.1795f64, // FORM
    0.258f64, // ZRX
];
pub static BN_LOT_SIZE: [f64; 3] = [
    1f64, // LDO
    0.1f64, // FORM
    0.1f64, // ZRX
];
pub static BN_MIN_NATIONAL: [f64; 3] = [
    5f64, // LDO
    5f64, // FORM
    5f64, // ZRX
];
pub static BN_MIN_QTY: [f64; 3] = [
    1f64, // LDO
    0.1f64, // FORM
    0.1f64, // ZRX
];
