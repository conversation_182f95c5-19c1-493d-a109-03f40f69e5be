pub static GATE_BASE_ASSETS: &[&str] = &[
    "APE",
];
pub const CURRENCY_LEN: usize = 1;
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum GateBaseAssetCurrency {
    APE,
}
impl GateBaseAssetCurrency {
    pub fn to_str(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::APE => "APE",
        }
    }
    pub fn from_symbol(symbol: &str) -> Option<Self> {
        let upper_symbol = symbol.to_uppercase();
        let symbol = upper_symbol.trim_end_matches("USDT").trim_end_matches('_');
        match symbol {
            "APE" => Some(GateBaseAssetCurrency::APE),
            _ => None,
        }
    }
    pub fn to_bn_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::APE => "APEUSDT",
        }
    }
    pub fn to_gate_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::APE => "APE_USDT",
        }
    }
    pub fn from_usize(num: usize) -> Option<Self> {
        match num {
            0 => Some(GateBaseAssetCurrency::APE),
            _ => None,
        }
    }
}
// bn and gate bbo map
 //[
//(bn_best_bid_price, bn_best_bid_qty, bn_best_ask_price, bn_best_ask_qty), 
//(gate_best_bid_price, gate_best_bid_qty, gate_best_ask_price, gate_best_ask_qty)]
pub static mut BN_AND_GATE_BBO_MAP: [([f64; 4], [f64; 4]); 1] = [([0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]); 1];
use crate::encoding::gate;
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct GateMakerOrder {
    pub req_id: u64,
    pub order_id: u64,
    pub price: f64,
    pub status: gate::UnifiedOrderStatus,
}
// req id, order id, order price, order status(0 for pending, 1 for ack
pub static mut GATE_PENDING_ORDERS: [GateMakerOrder; 1] = [GateMakerOrder { req_id: 0, order_id: 0, price: 0.0, status: gate::UnifiedOrderStatus::New }; 1];
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct BnHedgeOrder { pub qty: f64, pub order_id: u64 }
pub static mut BN_HEDGE_ORDERS: [BnHedgeOrder; 1] = [BnHedgeOrder { qty: 0.0, order_id: 0 }; 1];
pub static GATE_PRICE_TICKS: [f64; 1] = [
    0.0001f64, // APE
];
pub static GATE_QUANTO_MULTIPLIER: [f64; 1] = [
    1f64, // APE
];

// 0 for bn avg price, 1 for bn qty, 2 for gate avg price, 3 for gate qty
pub static mut HEDGE_STATUS: [(f64, f64, f64, f64); 1] = [(0.0, 0.0, 0.0, 0.0); 1];
