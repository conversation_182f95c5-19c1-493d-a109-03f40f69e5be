use crate::{
    CallbackData,
    Message,
    Result,
    Settings,
    WebSocket,
    WebSocketHandle,
    encoding::{
        agg_trades::parse_futures_agg_trade,
        book_ticker::parse_futures_bookticker,
        futures_order::generate_futures_user_data_ping_request,
        futures_order_response::{
            FuturesOrderResponse, OrderStatus, UserDataResponse, parse_futures_order_response,
            parse_user_data_response,
        },
        futures_orderbook::{
            parse_futures_orderbook_snapshot, parse_futures_orderbook_update,
            parse_http_orderbook_snapshot,
        },
    },
    engine::{
        arbitrage_engine_futures::ArbitrageEngineFutures,
        binance::{
            generate_futures_agg_trade_url, generate_futures_book_ticker_url,
            generate_futures_depth_diff_url, generate_futures_depth_snapshot_url,
            generate_futures_order_url,
        },
        futures_const::{
            FUTURES_IN_LEN, FUTURES_OUT_LEN, FUTURES_USDC_SYMBOL, FUTURES_USDT_SYMBOL,
        },
        token::*,
        trade::{
            generate_futures_session_logon_request, generate_futures_user_data_start_request,
            generate_futures_user_data_stream_url,
        },
    },
    error,
    flush_logs,
    info,
    logln,
    net::{message::http::StatusCode, utils::url::Url},
    // resources::futures_resources::generate_futures_depth_snapshot_req,
    utils::perf::system_now_in_us,
};

pub fn callback()
-> impl FnMut(&mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>, CallbackData) -> Result<()> {
    let mut arbitrage_engine = ArbitrageEngineFutures::new();
    let mut listen_key_created = false;
    let mut last_listen_key_ping_time = system_now_in_us();
    let mut _listen_key = String::new();

    move |handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
          cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    WS_BBO_1 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            if arbitrage_engine.update_book_ticker(&bt) {
                                // arbitrage_engine.try_take_profit_or_stop_loss(handle)?;
                                if let Some((price, side)) =
                                    arbitrage_engine.try_orderbook_arbitrage()
                                {
                                    arbitrage_engine.place_order(handle, price, side)?;
                                } else {
                                    arbitrage_engine.cancel_all_orders(handle)?;
                                }
                            }
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                        if system_now_in_us() - last_listen_key_ping_time > 300_000_000 {
                            handle.send_message(
                                ORDER_TOKEN_1,
                                generate_futures_user_data_ping_request(),
                            )?;
                            last_listen_key_ping_time = system_now_in_us();
                        }
                    }
                    WS_DEPTH_SS_1 => {
                        if let Some(depth) = parse_futures_orderbook_snapshot(data.as_ref()) {
                            if arbitrage_engine.update_orderbook_snapshot(&depth) {
                                if let Some((price, side)) =
                                    arbitrage_engine.try_orderbook_arbitrage()
                                {
                                    arbitrage_engine.place_order(handle, price, side)?;
                                } else {
                                    arbitrage_engine.cancel_all_orders(handle)?;
                                }
                            }
                        } else {
                            logln!("failed to parse market data");
                        }
                    }
                    WS_DEPTH_DF_T_1 => {
                        if let Some(depth_diff) = parse_futures_orderbook_update(data.as_ref()) {
                            if arbitrage_engine.update_orderbook_diff(&depth_diff) {
                                if let Some((price, side)) =
                                    arbitrage_engine.try_orderbook_arbitrage()
                                {
                                    arbitrage_engine.place_order(handle, price, side)?;
                                } else {
                                    arbitrage_engine.cancel_all_orders(handle)?;
                                }
                            } else {
                                info!("depth diff not updated");
                            }
                        } else {
                            info!("failed to parse depth diff");
                        }
                    }
                    WS_AGG_TRADE_T_1 => {
                        if let Some(trade) = parse_futures_agg_trade(data.as_ref()) {
                            arbitrage_engine.add_agg_trades(&trade);
                            if let Some(op) = arbitrage_engine.try_agg_trades_arbitrage() {
                                arbitrage_engine.place_order(handle, op.0, op.1)?;
                            }
                        } else {
                            error!(
                                "failed to parse agg trade: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    ORDER_TOKEN_1 => {
                        if !listen_key_created {
                            handle
                                .send_message(token, generate_futures_user_data_start_request())?;
                            listen_key_created = true;
                        }
                        match parse_futures_order_response(data.as_ref()) {
                            Some(FuturesOrderResponse::ListenKey(key)) => {
                                info!("listen key: {}", key);
                                _listen_key = key;
                                last_listen_key_ping_time = system_now_in_us();
                                flush_logs!();
                                let url =
                                    generate_futures_user_data_stream_url(_listen_key.clone());
                                handle.connect(url.into(), USER_DATA_STREAM_1, None)?;
                            }
                            Some(FuturesOrderResponse::Error(order_error)) => {
                                if let Some(id) = order_error.id {
                                    crate::info!("order response error: {}", order_error);
                                    arbitrage_engine.remove_order_by_id(id, handle)?;
                                }
                                if order_error.error.contains("5022")
                                    || order_error.error.contains("2011")
                                {
                                    return Ok(());
                                }
                                crate::info!("order response error: {}", order_error);
                            }
                            Some(FuturesOrderResponse::OrderResponse(order_id, order_status)) => {
                                if order_status == OrderStatus::Canceled {
                                    crate::info!("order canceled: {}", order_id);
                                    arbitrage_engine.remove_order_by_id(order_id, handle)?;
                                }
                            }
                            Some(FuturesOrderResponse::Unkown(id)) => {
                                crate::info!(
                                    "order response unkown: {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                                if let Some(id) = id {
                                    arbitrage_engine.remove_order_by_id(id, handle)?;
                                }
                            }
                            None => {
                                crate::debug!(
                                    "order response is none: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                    }
                    USER_DATA_STREAM_1 => {
                        if let Some(order_trade_update) = parse_user_data_response(data.as_ref()) {
                            crate::debug!("recv user data: {:?}", order_trade_update);
                            match order_trade_update {
                                UserDataResponse::OrderTradeUpdate(order_trade_update) => {
                                    if order_trade_update.order_status == OrderStatus::Filled {
                                        crate::info!("order filled: {:?}", order_trade_update);
                                        arbitrage_engine
                                            .update_trade(&order_trade_update, handle)?;
                                    }
                                    if order_trade_update.order_status != OrderStatus::New
                                        && order_trade_update.order_status
                                            != OrderStatus::PartiallyFilled
                                    {
                                        crate::info!("recv user data: {:?}", order_trade_update);
                                        arbitrage_engine.remove_order_by_id(
                                            order_trade_update.order_id,
                                            handle,
                                        )?;
                                    }
                                }
                            }
                        } else {
                            crate::debug!(
                                "recv unknown user data: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => match token {
                    HTTP_ORDERBOOK_SNAPSHOT_1 => {
                        if response.status.unwrap() != StatusCode::OK || response.body.is_none() {
                            info!("orderbook snapshot request failed {:?}", response);
                            flush_logs!();
                            return Ok(());
                        }
                        if let Some(depth) =
                            parse_http_orderbook_snapshot(response.body.as_ref().unwrap().as_ref())
                        {
                            if !arbitrage_engine.update_orderbook_snapshot(&depth) {
                                info!("orderbook snapshot not updated");
                            }
                        } else {
                            info!("failed to parse orderbook snapshot");
                        }
                        flush_logs!();
                    }
                    _ => (),
                },
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                ORDER_TOKEN_1 => {
                    info!("Order connection opened: {:?}", token);
                    let req = generate_futures_session_logon_request();
                    handle.send_message(token, req)?;
                }
                USER_DATA_STREAM_1 => {
                    info!("user data stream connected");
                    flush_logs!();
                }
                HTTP_ORDERBOOK_SNAPSHOT_1 => {}
                _ => (),
            },
            CallbackData::ConnectionClose(_token, _err) => {}
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    }
}

pub fn run() -> Result<()> {
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback())?;

    let bbo_url: Url =
        generate_futures_book_ticker_url(&[FUTURES_USDT_SYMBOL, FUTURES_USDC_SYMBOL]).into();
    info!("bbo url: {}", bbo_url);
    websocket.connect(bbo_url.clone(), WS_BBO_1)?;

    let depth_snapshot_url: Url = generate_futures_depth_snapshot_url(FUTURES_USDT_SYMBOL).into();
    info!("depth snapshot url: {}", depth_snapshot_url);
    websocket.connect(depth_snapshot_url.clone(), WS_DEPTH_SS_1)?;

    let agg_trade_url: Url = generate_futures_agg_trade_url(FUTURES_USDT_SYMBOL).into();
    info!("agg trade url: {}", agg_trade_url);
    websocket.connect(agg_trade_url.clone(), WS_AGG_TRADE_T_1)?;

    let depth_diff_url: Url = generate_futures_depth_diff_url(FUTURES_USDT_SYMBOL).into();
    info!("depth diff url: {}", depth_diff_url);
    websocket.connect(depth_diff_url.clone(), WS_DEPTH_DF_T_1)?;

    let order_url: Url = generate_futures_order_url().into();
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), ORDER_TOKEN_1)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            error!("Websocket run error: {:?}", e);
            flush_logs!();
        }
    }
    Ok(())
}
