use crate::{
    encoding::{
        book_ticker::FuturesBookTicker,
        futures_order_response::OrderStatus,
        gate::{self, GateBookTicker},
    },
    engine::{
        gate_const::{
            BN_AND_GATE_BBO_MAP, BN_HEDGE_ORDERS, BnHedgeOrder, CURRENCY_LEN, GATE_PENDING_ORDERS,
            GATE_PRICE_TICKS, GateBaseAssetCurrency, GateMakerOrder, HEDGE_STATUS,
        },
        trading_pair::PRICE_TICK,
    },
    utils::perf::now,
};

fn find_nearest_price_by_tick(price: f64, tick: f64) -> f64 {
    let mut nearest_price = price;
    loop {
        nearest_price -= tick;
        if nearest_price < price * 0.9992 {
            return nearest_price;
        }
    }
}

pub struct ArbitrageGate {}

impl ArbitrageGate {
    pub fn new() -> Self {
        Self {}
    }

    pub fn update_bn_bbo(&mut self, bbo: &FuturesBookTicker) -> Option<GateMakerOrder> {
        let currency = match GateBaseAssetCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return None,
        };
        let index = currency as usize;
        unsafe {
            BN_AND_GATE_BBO_MAP[index].0 = [bbo.bid_price, bbo.bid_qty, bbo.ask_price, bbo.ask_qty];
            let order = GATE_PENDING_ORDERS[index];

            let tick = GATE_PRICE_TICKS[index];
            let req_id = now();
            if order.status == gate::UnifiedOrderStatus::Uninitialized {
                let order_price = find_nearest_price_by_tick(bbo.bid_price, tick);
                crate::info_unsafe!(
                    "placing gate order: bn price: {} gate price: {}",
                    bbo.bid_price,
                    order_price,
                );
                GATE_PENDING_ORDERS[index].price = order_price;
                GATE_PENDING_ORDERS[index].req_id = req_id;
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Opening;
                return Some(GATE_PENDING_ORDERS[index].clone());
            }

            if order.status == gate::UnifiedOrderStatus::Opening
                || order.status == gate::UnifiedOrderStatus::Amending
            {
                // pending orders
                crate::info_unsafe!("skip pending order {} {:?}", order.order_id, order.status);
                return None;
            }

            let diff_ratio = (bbo.bid_price - order.price) / bbo.bid_price;
            if diff_ratio > 0.001 || diff_ratio < 0.0008 {
                let order_price = find_nearest_price_by_tick(bbo.bid_price, tick);
                if (order_price - order.price).abs() < tick {
                    return None;
                }
                GATE_PENDING_ORDERS[index].price = order_price;
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Amending;
                GATE_PENDING_ORDERS[index].req_id = req_id;
                crate::info_unsafe!(
                    "amending gate order: bn price: {} gate price: {}",
                    bbo.bid_price,
                    order_price,
                );
                return Some(GATE_PENDING_ORDERS[index].clone());
            }
        }
        None
    }

    pub fn update_gate_bbo(&mut self, bbo: &GateBookTicker) -> Option<BnHedgeOrder> {
        let currency = match GateBaseAssetCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return None,
        };
        let index = currency as usize;
        unsafe {
            BN_AND_GATE_BBO_MAP[index].1 = [bbo.bid_price, bbo.bid_qty, bbo.ask_price, bbo.ask_qty];
        }
        let order = unsafe { GATE_PENDING_ORDERS[index] };
        if order.status == gate::UnifiedOrderStatus::Opening
            || order.status == gate::UnifiedOrderStatus::Amending
        {
            // pending order
            return None;
        }
        if bbo.bid_price <= order.price {
            crate::info!(
                "order filled by bbo {} {} {}",
                order.order_id,
                bbo.symbol,
                order.price
            );
            unsafe {
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Uninitialized;
                GATE_PENDING_ORDERS[index].req_id = 0;
                let new_qty = BN_HEDGE_ORDERS[index].qty + HEDGE_STATUS[index].3;
                let new_price = (order.price * BN_HEDGE_ORDERS[index].qty
                    + HEDGE_STATUS[index].2 * HEDGE_STATUS[index].3)
                    / new_qty;
                HEDGE_STATUS[index].2 = new_price;
                HEDGE_STATUS[index].3 = new_qty;
                BN_HEDGE_ORDERS[index].qty = PRICE_TICK[index];
                BN_HEDGE_ORDERS[index].order_id = now();
                return Some(BN_HEDGE_ORDERS[index].clone());
            }
        }
        None
    }

    pub fn update_bn_order_status(
        &mut self,
        status: OrderStatus,
        order_id: u64,
        price: f64,
        qty: f64,
    ) {
        if status != OrderStatus::Filled {
            return;
        }
        let mut target_index = 0;
        for i in 0..CURRENCY_LEN {
            unsafe {
                if BN_HEDGE_ORDERS[i].order_id == order_id {
                    BN_HEDGE_ORDERS[i].order_id = 0;
                    target_index = i;
                    break;
                }
            }
        }
        unsafe {
            let new_qty = qty + HEDGE_STATUS[target_index].1;
            let new_price = (price * qty
                + HEDGE_STATUS[target_index].0 * HEDGE_STATUS[target_index].1)
                / new_qty;
            HEDGE_STATUS[target_index].0 = new_price;
            HEDGE_STATUS[target_index].1 = new_qty;
            crate::info_unsafe!(
                r#"
Hedge status:
{:?}
bn price: {}, bn qty: {}
gate price: {}, gate qty: {}
"#,
                GateBaseAssetCurrency::from_usize(target_index).unwrap(),
                HEDGE_STATUS[target_index].0,
                HEDGE_STATUS[target_index].1,
                HEDGE_STATUS[target_index].2,
                HEDGE_STATUS[target_index].3,
            );
        }
    }

    pub fn update_gate_order_status(
        &mut self,
        status: gate::UnifiedOrderStatus,
        symbol: &str,
        order_id: u64,
        req_id: u64,
        price: f64,
    ) -> Option<BnHedgeOrder> {
        let currency = match GateBaseAssetCurrency::from_symbol(symbol) {
            Some(currency) => currency,
            None => {
                let mut target = 10000;
                let len = CURRENCY_LEN;
                for i in 0..len {
                    let p = unsafe { GATE_PENDING_ORDERS[i] };
                    if p.req_id == req_id {
                        target = i;
                        break;
                    }
                }
                if target != 10000 {
                    GateBaseAssetCurrency::from_usize(target).unwrap()
                } else {
                    crate::error!("unknown symbol {}", req_id);
                    return None;
                }
            }
        };
        let index = currency as usize;
        match status {
            gate::UnifiedOrderStatus::Open => unsafe {
                if GATE_PENDING_ORDERS[index].status != gate::UnifiedOrderStatus::Opening
                    && GATE_PENDING_ORDERS[index].status != gate::UnifiedOrderStatus::Amending
                {
                    crate::error_unsafe!("order opened but not pending, {} {}", order_id, symbol);
                    return None;
                }
                GATE_PENDING_ORDERS[index].price = price;
                GATE_PENDING_ORDERS[index].order_id = order_id;
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Open;
                crate::info_unsafe!("order opened {} {} {}", order_id, symbol, price);
                None
            },
            gate::UnifiedOrderStatus::Filled => unsafe {
                crate::info_unsafe!(
                    "order filled by order rsp {} {} {}",
                    order_id,
                    symbol,
                    price
                );
                if GATE_PENDING_ORDERS[index].status != gate::UnifiedOrderStatus::Open
                    || GATE_PENDING_ORDERS[index].order_id != order_id
                {
                    crate::error_unsafe!("order filled but not open, {} {}", order_id, symbol);
                    return None;
                }
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Uninitialized;
                GATE_PENDING_ORDERS[index].req_id = 0;
                BN_HEDGE_ORDERS[index].qty = PRICE_TICK[index];
                BN_HEDGE_ORDERS[index].order_id = now();
                let new_qty = BN_HEDGE_ORDERS[index].qty + HEDGE_STATUS[index].3;
                let new_price = (price * BN_HEDGE_ORDERS[index].qty
                    + HEDGE_STATUS[index].2 * HEDGE_STATUS[index].3)
                    / new_qty;
                HEDGE_STATUS[index].2 = new_price;
                HEDGE_STATUS[index].3 = new_qty;
                Some(BN_HEDGE_ORDERS[index].clone())
            },
            gate::UnifiedOrderStatus::Canceled | gate::UnifiedOrderStatus::Expired => unsafe {
                crate::info_unsafe!("order canceled {} {} {}", order_id, symbol, price);
                GATE_PENDING_ORDERS[index].status = gate::UnifiedOrderStatus::Uninitialized;
                GATE_PENDING_ORDERS[index].req_id = 0;
                None
            },
            _ => None,
        }
    }
}
