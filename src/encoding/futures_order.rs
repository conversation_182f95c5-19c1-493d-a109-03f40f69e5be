use std::str::FromStr;

use crate::{
    engine::{
        futures_const::{
            FUTURES_API_KEY, FUTURES_ORDER_QUANTITY, FUTURES_QUANTITY_TICK_SIZE,
            FUTURES_USDC_SYMBOL,
        },
        opportunity_simulator::{Opportunity, OpportunityType},
    },
    utils::perf::system_now_in_us,
};

#[derive(PartialEq, Eq, PartialOrd, Ord, Clone, Copy, Debug)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl OrderSide {
    pub fn to_string(&self) -> &str {
        match self {
            OrderSide::Buy => "BUY",
            OrderSide::Sell => "SELL",
        }
    }

    pub fn sign(&self) -> f64 {
        match self {
            OrderSide::Buy => 1.0,
            OrderSide::Sell => -1.0,
        }
    }

    pub fn reverse(&self) -> OrderSide {
        match self {
            OrderSide::Buy => OrderSide::Sell,
            OrderSide::Sell => OrderSide::Buy,
        }
    }
}

impl FromStr for OrderSide {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "BUY" => Ok(OrderSide::Buy),
            "SELL" => Ok(OrderSide::Sell),
            _ => Err(()),
        }
    }
}

pub fn generate_futures_user_data_ping_request() -> String {
    format!(
        r#"{{
  "id": "815d5fce-0880-4287-a567-80badf004c74",
  "method": "userDataStream.ping",
  "params": {{
    "apiKey": "{}"
    }}
}}"#,
        FUTURES_API_KEY
    )
}

pub fn generate_futures_order_request(
    price: f64,
    side: OrderSide,
    symbol: &str,
    order_id: u64,
) -> String {
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {:.2},
        "newClientOrderId": "{}",
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "GTX",
        "timestamp": {},
        "type": "LIMIT"
    }}
}}"#,
        order_id,
        price,
        order_id,
        FUTURES_ORDER_QUANTITY,
        side.to_string(),
        symbol,
        order_id / 1000,
    );
    crate::info!(
        "Order request: id: {} side: {} price: {}, qty: {}",
        order_id,
        side.to_string(),
        price,
        FUTURES_ORDER_QUANTITY,
    );
    reuslt
}

pub fn generate_futures_market_order_request(
    side: OrderSide,
    qty: f64,
    symbol: &str,
    order_id: u64,
) -> String {
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "newClientOrderId": "{}",
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "GTC",
        "timestamp": {},
        "type": "MARKET"
    }}
}}"#,
        order_id,
        order_id,
        qty,
        side.to_string(),
        symbol,
        order_id / 1000,
    );
    crate::info!(
        "Order request: id: {} side: {} qty: {}",
        order_id,
        side.to_string(),
        qty
    );
    reuslt
}

pub fn generate_take_profit_and_stop_loss_order_request(
    opportunity: &Opportunity,
    symbol: &str,
) -> String {
    let order_type = match opportunity.opportunity_type {
        OpportunityType::TakeProfit => "TAKE_PROFIT",
        OpportunityType::StopLoss => "STOP",
        _ => panic!(
            "Invalid opportunity type: {:?}",
            opportunity.opportunity_type
        ),
    };
    let price = opportunity.price;
    let stop_price = opportunity.stop_price;
    let side = opportunity.side;
    let order_id = opportunity.id;
    let result = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {:.2},
        "stopPrice": {:.2},
        "newClientOrderId": "{}",
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "GTX",
        "timestamp": {},
        "type": "{}"
    }}
}}"#,
        order_id,
        price,
        stop_price,
        order_id,
        FUTURES_ORDER_QUANTITY,
        side.to_string(),
        symbol,
        order_id / 1000,
        order_type,
    );
    crate::debug!(
        "Order request: side: {} price: {}, qty: {}",
        side.to_string(),
        price,
        FUTURES_QUANTITY_TICK_SIZE,
    );
    result
}

pub fn generate_futures_reduce_only_request(
    price: f64,
    side: OrderSide,
    quantity: f64,
    symbol: &str,
) -> String {
    let order_id = system_now_in_us();
    let quantity = (quantity / FUTURES_QUANTITY_TICK_SIZE).round() * FUTURES_QUANTITY_TICK_SIZE;
    let result = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "newClientOrderId": "{}",
        "price": {:.2},
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "IOC",
        "timestamp": {},
        "type": "LIMIT",
        "reduceOnly": true
    }}
}}"#,
        order_id,
        order_id,
        price,
        quantity,
        side.to_string(),
        symbol,
        order_id / 1000,
    );
    crate::debug!("Order request: {}", result);
    result
}

pub fn generate_futures_cancel_order_request(order_id: u64) -> String {
    /*
        {
           "id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
        "method": "order.cancel",
        "params": {
            "apiKey": "HsOehcfih8ZRxnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGOGQj1lGdTjR",
            "orderId": 283194212,
            "symbol": "BTCUSDT",
            "timestamp": 1703439070722,
            "signature": "b09c49815b4e3f1f6098cd9fbe26a933a9af79803deaaaae03c29f719c08a8a8"
        }
    } */

    let now = system_now_in_us();
    let result = format!(
        r#"
{{
    "id": "{}",
    "method": "order.cancel",
    "params": {{
        "origClientOrderId": "{}",
        "symbol": "{}",
        "timestamp": {}
    }}
}}
"#,
        order_id,
        order_id,
        FUTURES_USDC_SYMBOL,
        now / 1000
    );
    crate::debug!("Cancel order request: order_id: {}", order_id);
    result
}
