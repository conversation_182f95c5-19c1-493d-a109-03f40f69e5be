use memchr;
use std::str;

use crate::{Currency, TradingPair};

#[derive(Debug)]
pub struct OrderId {
    pub order_create_time: u64, // it it not an timestamp, but a time represents by cpu circles
    pub ring_index: usize,
    pub edge_index: usize,
    pub is_testing: bool,
}

#[derive(Debug)]
pub struct OrderCreated {
    pub order_id: OrderId,
    pub symbol: TradingPair,
}

impl OrderCreated {
    pub fn new(order_id: OrderId, symbol: TradingPair) -> Self {
        OrderCreated { order_id, symbol }
    }
}

#[derive(Debug)]
pub struct OrderUpdate {
    pub order_id: OrderId,
    pub fill_price: f64,
    pub fill_quantity: f64,
    pub symbol: TradingPair,
    pub status: String,          // 执行状态：NEW, TRADE, FILLED 等
    pub orig_quantity: f64,      // 原始订单数量
    pub executed_quantity: f64,  // 已执行数量
    pub remaining_quantity: f64, // 剩余数量
    pub order_submited_time: u64,
    pub order_trasaction_time: u64,
    pub is_maker: bool,
}

#[derive(Debug)]
pub struct Balance {
    pub asset: Currency, // 资产类型
    pub free: f64,       // 可用余额
    pub locked: f64,     // 锁定余额
}

#[derive(Debug)]
pub struct AccountPosition {
    pub event_time: u64,        // 事件时间
    pub last_update_time: u64,  // 最后账户更新时间
    pub balances: Vec<Balance>, // 余额数组
}

#[derive(Debug)]
pub struct OrderErr {
    pub order_id: OrderId,
    pub error_message: String,
}

#[derive(Debug)]
pub enum OrderResponse {
    OrderCreated(OrderCreated),
    OrderUpdate(OrderUpdate),
    AccountPosition(AccountPosition),
    OrderError(OrderErr),
}

impl From<&str> for OrderId {
    fn from(s: &str) -> Self {
        let parts = s.split('-').collect::<Vec<&str>>();
        let order_create_time = match parts[0].parse::<u64>() {
            Ok(n) => n,
            Err(_) => {
                return OrderId {
                    order_create_time: 0,
                    ring_index: 0,
                    edge_index: 0,
                    is_testing: false,
                };
            }
        };
        let ring_index = parts[1].parse::<usize>().unwrap();
        let edge_index = parts[2].parse::<usize>().unwrap();
        // 如果有第4个部分，则解析 is_testing 标志，否则默认为 false
        let is_testing = if parts.len() > 3 {
            parts[3] == "1"
        } else {
            false
        };
        OrderId {
            order_create_time,
            ring_index,
            edge_index,
            is_testing,
        }
    }
}

impl OrderUpdate {
    pub fn new(order_id: OrderId, symbol: TradingPair, status: String) -> Self {
        OrderUpdate {
            order_id,
            fill_price: 0.0,
            fill_quantity: 0.0,
            symbol,
            status,
            orig_quantity: 0.0,
            executed_quantity: 0.0,
            remaining_quantity: 0.0,
            order_submited_time: 0,
            order_trasaction_time: 0,
            is_maker: false,
        }
    }
}

fn parse_account_position(input: &[u8]) -> Option<OrderResponse> {
    // 解析事件时间 "E": ************* 或 "E": *************
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)? + event_time_pattern.len();

    // 跳过可能的空格
    let mut pos = start;
    while pos < input.len() && input[pos] == b' ' {
        pos += 1;
    }

    let end = memchr::memchr(b',', &input[pos..])?;
    let event_time_str = &input[pos..pos + end];
    let event_time_str: &str = unsafe { std::mem::transmute(event_time_str) };
    let event_time = event_time_str.trim().parse().ok()?;

    // 解析最后更新时间 "u": *************
    let last_update_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, last_update_pattern)?;
    let start = start + last_update_pattern.len();

    // 跳过可能的空格
    let mut pos = start;
    while pos < input.len() && input[pos] == b' ' {
        pos += 1;
    }

    let end = memchr::memchr(b',', &input[pos..])?;
    let last_update_str = &input[pos..pos + end];
    let last_update_str: &str = unsafe { std::mem::transmute(last_update_str) };
    let last_update_time = last_update_str.trim().parse().ok()?;

    // 解析余额数组 "B": [...]
    let balances_pattern = b"\"B\":[";
    let balances_start = memchr::memmem::find(input, balances_pattern)? + balances_pattern.len();

    // 找到余额数组的结束位置
    let balances_end = find_array_end(&input[balances_start..])?;
    let balances_data = &input[balances_start..balances_start + balances_end];

    let balances = parse_balances(balances_data)?;

    Some(OrderResponse::AccountPosition(AccountPosition {
        event_time,
        last_update_time,
        balances,
    }))
}

fn find_array_end(data: &[u8]) -> Option<usize> {
    let mut bracket_count = 1; // 开始时已经在数组内部，所以从1开始
    let mut in_string = false;
    let mut escape_next = false;

    for (i, &byte) in data.iter().enumerate() {
        if escape_next {
            escape_next = false;
            continue;
        }

        match byte {
            b'\\' if in_string => escape_next = true,
            b'"' => in_string = !in_string,
            b'[' if !in_string => bracket_count += 1,
            b']' if !in_string => {
                bracket_count -= 1;
                if bracket_count == 0 {
                    return Some(i);
                }
            }
            _ => {}
        }
    }
    None
}

fn parse_balances(data: &[u8]) -> Option<Vec<Balance>> {
    let mut balances = Vec::new();
    let mut pos = 0;

    while pos < data.len() {
        // 查找下一个对象的开始 "{"
        if let Some(obj_start) = memchr::memchr(b'{', &data[pos..]) {
            pos += obj_start + 1;
        } else {
            break;
        }

        // 查找对象的结束 "}"
        if let Some(obj_end) = memchr::memchr(b'}', &data[pos..]) {
            let obj_data = &data[pos..pos + obj_end];

            // 解析单个余额对象
            if let Some(balance) = parse_single_balance(obj_data) {
                balances.push(balance);
            }

            pos += obj_end + 1;
        } else {
            break;
        }

        // 查找下一个对象，如果没有找到逗号就结束
        if let Some(comma_pos) = memchr::memchr(b',', &data[pos..]) {
            pos += comma_pos + 1;
        } else {
            break;
        }
    }

    Some(balances)
}

fn parse_single_balance(data: &[u8]) -> Option<Balance> {
    // 解析资产 "a": "ETH"
    let asset_pattern = b"\"a\":\"";
    let start = memchr::memmem::find(data, asset_pattern)? + asset_pattern.len();
    let end = memchr::memchr(b'"', &data[start..])?;
    let asset_str = &data[start..start + end];
    let asset_str: &str = unsafe { std::mem::transmute(asset_str) };
    let asset = Currency::from_str(asset_str);

    // 解析可用余额 "f": "10000.000000"
    let free_pattern = b"\"f\":\"";
    let start = memchr::memmem::find(data, free_pattern)? + free_pattern.len();
    let end = memchr::memchr(b'"', &data[start..])?;
    let free_str = &data[start..start + end];
    let free_str: &str = unsafe { std::mem::transmute(free_str) };
    let free = free_str.parse().ok()?;

    // 解析锁定余额 "l": "0.000000"
    let locked_pattern = b"\"l\":\"";
    let start = memchr::memmem::find(data, locked_pattern)? + locked_pattern.len();
    let end = memchr::memchr(b'"', &data[start..])?;
    let locked_str = &data[start..start + end];
    let locked_str: &str = unsafe { std::mem::transmute(locked_str) };
    let locked = locked_str.parse().ok()?;

    Some(Balance {
        asset,
        free,
        locked,
    })
}

// #[perf_macro::measure]
pub fn parse_order_update(input: &[u8]) -> Option<OrderResponse> {
    // 首先检查是否是 outboundAccountPosition 事件
    let account_position_pattern = b"\"e\":\"outboundAccountPosition\"";
    if memchr::memmem::find(input, account_position_pattern).is_some() {
        // 进一步验证这确实是一个 outboundAccountPosition 事件
        // 检查是否包含必需的字段 "u": 和 "B":
        if memchr::memmem::find(input, b"\"u\":").is_some()
            && memchr::memmem::find(input, b"\"B\":[").is_some()
        {
            return parse_account_position(input);
        }
    }

    let error_pattern2 = b"\"error\":{";
    let error_start = memchr::memmem::find(input, error_pattern2);
    if let Some(error_start) = error_start {
        let id_pattern = b"\"id\":\"";
        let id_start = memchr::memmem::find(input, id_pattern)? + id_pattern.len();
        let id_end = memchr::memchr(b'"', &input[id_start..])?;
        let id = &input[id_start..id_start + id_end];
        let id: &str = unsafe { std::mem::transmute(id) };
        let order_id: OrderId = id.into();
        let msg_pattern2 = b"\"msg\":\"";
        let msg_start =
            if let Some(start) = memchr::memmem::find(&input[error_start..], msg_pattern2) {
                Some(error_start + start + msg_pattern2.len())
            } else {
                None
            };

        if let Some(msg_start) = msg_start {
            if let Some(msg_end) = memchr::memchr(b'"', &input[msg_start..]) {
                let error_msg = &input[msg_start..msg_start + msg_end];
                let error_msg: &str = unsafe { std::mem::transmute(error_msg) };
                return Some(OrderResponse::OrderError(OrderErr {
                    order_id,
                    error_message: error_msg.to_owned(),
                }));
            }
        }
        // 如果无法解析具体错误信息，返回通用错误
        return Some(OrderResponse::OrderError(OrderErr {
            order_id,
            error_message: "Order failed".to_string(),
        }));
    }

    let order_response_pattern = b"tOrderId\":\"";
    match memchr::memmem::find(input, order_response_pattern) {
        Some(start) => {
            let start = start + order_response_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let order_id = &input[start..start + end];
            let order_id: &str = unsafe { std::mem::transmute(order_id) };
            let order_id: OrderId = order_id.into();
            if order_id.order_create_time == 0 {
                return None;
            }

            // 解析 symbol
            let start = memchr::memmem::find(input, b"\"symbol\":\"")? + 10;
            let end = memchr::memchr(b'"', &input[start..])?;
            let symbol = &input[start..start + end];
            let symbol: &str = unsafe { std::mem::transmute(symbol) };
            let symbol = TradingPair::from(symbol);

            // 创建 OrderCreated
            let order_created = OrderCreated::new(order_id, symbol);

            Some(OrderResponse::OrderCreated(order_created))
        }
        None => {
            let order_update_pattern = b"executionReport";
            let _execution_report_pos = memchr::memmem::find(input, order_update_pattern)?;
            let status_pattern = b"x\":\"";
            let status_start = memchr::memmem::find(input, status_pattern)?;
            let start = status_start + status_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let exec_status = &input[start..start + end];
            let exec_status: &str = unsafe { std::mem::transmute(exec_status) };
            // Handle any execution status (NEW, TRADE, FILLED, REJECTED, EXPIRED, CANCELED, etc.)
            if exec_status == "TRADE"
                || exec_status == "NEW"
                || exec_status == "FILLED"
                || exec_status == "REJECTED"
                || exec_status == "EXPIRED"
                || exec_status == "CANCELED"
            {
                let order_id_pattern = b"\"c\":\"";
                let start = memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
                let end = memchr::memchr(b'"', &input[start..])?;
                let order_id = &input[start..start + end];
                let order_id: &str = unsafe { std::mem::transmute(order_id) };
                let mut order_id: OrderId = order_id.into();
                if order_id.order_create_time == 0 {
                    let order_id_pattern = b"\"C\":\"";
                    let start =
                        memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
                    let end = memchr::memchr(b'"', &input[start..])?;
                    let order_id_str = &input[start..start + end];
                    let order_id_str: &str = unsafe { std::mem::transmute(order_id_str) };
                    order_id = order_id_str.into();
                    if order_id.order_create_time == 0 {
                        return None;
                    }
                }

                let symbol_pattern = b"\"s\":\"";
                let symbol = memchr::memmem::find(input, symbol_pattern)? + symbol_pattern.len();
                let end = memchr::memchr(b'"', &input[symbol..])?;
                let symbol = &input[symbol..symbol + end];
                let symbol: &str = unsafe { std::mem::transmute(symbol) };
                let symbol = TradingPair::from(symbol);

                let mut order_update = OrderUpdate::new(order_id, symbol, exec_status.to_string());

                // 解析原始数量 (q field) - 支持有空格和无空格的格式
                let orig_qty_pattern = b"\"q\":\""; // 无空格的格式

                let (start_pos, pattern_len) =
                    if let Some(start) = memchr::memmem::find(input, orig_qty_pattern) {
                        (start, orig_qty_pattern.len())
                    } else {
                        (0, 0) // 标记未找到
                    };

                if pattern_len > 0 {
                    let start = start_pos + pattern_len;
                    if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                        let orig_qty = &input[start..start + end];
                        let orig_qty: &str = unsafe { std::mem::transmute(orig_qty) };
                        if let Ok(qty) = orig_qty.parse::<f64>() {
                            order_update.orig_quantity = qty;
                            // 初始化剩余数量为原始数量
                            order_update.remaining_quantity = qty;
                        }
                    }
                }

                // 解析已执行数量 (z field) - 支持有空格和无空格的格式
                let executed_qty_pattern = b"\"z\":\""; // 无空格的格式

                let (start_pos, pattern_len) =
                    if let Some(start) = memchr::memmem::find(input, executed_qty_pattern) {
                        (start, executed_qty_pattern.len())
                    } else {
                        (0, 0) // 标记未找到
                    };

                if pattern_len > 0 {
                    let start = start_pos + pattern_len;
                    if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                        let executed_qty = &input[start..start + end];
                        let executed_qty: &str = unsafe { std::mem::transmute(executed_qty) };
                        if let Ok(qty) = executed_qty.parse::<f64>() {
                            order_update.executed_quantity = qty;
                            // 重新计算剩余数量
                            order_update.remaining_quantity = order_update.orig_quantity - qty;
                        }
                    }
                }

                // Parse fill price and quantity for TRADE status
                if exec_status == "TRADE" {
                    // Parse fill price (L field)
                    let price_pattern = b"\"L\":\"";
                    if let Some(start) = memchr::memmem::find(input, price_pattern) {
                        let start = start + price_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_price = &input[start..start + end];
                            let fill_price: &str = unsafe { std::mem::transmute(fill_price) };
                            if let Ok(price) = fill_price.parse::<f64>() {
                                order_update.fill_price = price;
                            }
                        }
                    }

                    // Parse fill quantity (l field - lowercase L)
                    let qty_pattern = b"\"l\":\"";
                    if let Some(start) = memchr::memmem::find(input, qty_pattern) {
                        let start = start + qty_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_qty = &input[start..start + end];
                            let fill_qty: &str = unsafe { std::mem::transmute(fill_qty) };
                            if let Ok(qty) = fill_qty.parse::<f64>() {
                                order_update.fill_quantity = qty;
                            }
                        }
                    }
                    let order_transaction_time_pattern = b"\"T\":";
                    let start = memchr::memmem::find(input, order_transaction_time_pattern)?
                        + order_transaction_time_pattern.len();
                    let end = memchr::memchr(b',', &input[start..])?;
                    let order_transaction_time = &input[start..start + end];
                    let order_transaction_time: &str =
                        unsafe { std::mem::transmute(order_transaction_time) };
                    order_update.order_trasaction_time = order_transaction_time.parse().unwrap();

                    let maker_pattern = b"\"m\":";
                    let start = memchr::memmem::find(input, maker_pattern)? + maker_pattern.len();
                    let end = memchr::memchr(b',', &input[start..])?;
                    let is_maker = &input[start..start + end];
                    let is_maker: &str = unsafe { std::mem::transmute(is_maker) };
                    order_update.is_maker = is_maker.parse().unwrap();
                }
                if exec_status == "NEW" {
                    let order_creat_patter = b"\"O\":";
                    let start =
                        memchr::memmem::find(input, order_creat_patter)? + order_creat_patter.len();
                    let end = memchr::memchr(b',', &input[start..])?;
                    let order_create_time = &input[start..start + end];
                    let order_create_time: &str = unsafe { std::mem::transmute(order_create_time) };
                    order_update.order_submited_time = order_create_time.parse().unwrap();
                }
                Some(OrderResponse::OrderUpdate(order_update))
            } else {
                None
            }
        }
    }
}
