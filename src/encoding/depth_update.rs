use memchr;
use std::str::from_utf8_unchecked;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DepthUpdate<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub final_update_id: u64,
    pub bid_updates: Vec<(f64, f64)>,
    pub ask_updates: Vec<(f64, f64)>,
}

pub fn parse_depth_update(input: &[u8]) -> Option<DepthUpdate<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];
    let event_time = unsafe { from_utf8_unchecked(event_time_str) }
        .parse()
        .unwrap();

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'\"', &input[start..])?;
    let symbol_str = &input[start..start + end];

    let final_update_id_parttern = b"\"u\":";
    let start = memchr::memmem::find(input, final_update_id_parttern)?;
    let start = start + final_update_id_parttern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let final_update_id_str = &input[start..start + end];

    let bids_pattern = b"\"b\":[[";
    let bids_end_pattern = b"]]";
    let bids = match memchr::memmem::find(input, bids_pattern) {
        Some(start) => {
            let start = start + bids_pattern.len();
            let end = memchr::memmem::find(&input[start..], bids_end_pattern)?;
            let bids_str = &input[start..start + end];
            parse_price_levels(bids_str)
        }
        None => Vec::new(),
    };

    let asks_pattern = b"\"a\":[[";
    let asks_end_pattern = b"]]";
    let asks = match memchr::memmem::find(input, asks_pattern) {
        Some(start) => {
            let start = start + asks_pattern.len();
            let end = memchr::memmem::find(&input[start..], asks_end_pattern)?;
            let asks_str = &input[start..start + end];
            parse_price_levels(asks_str)
        }
        None => Vec::new(),
    };

    let symbol = unsafe { from_utf8_unchecked(symbol_str) };
    let final_update_id = unsafe { from_utf8_unchecked(final_update_id_str) }
        .parse()
        .unwrap();

    Some(DepthUpdate {
        symbol,
        event_time,
        final_update_id,
        bid_updates: bids,
        ask_updates: asks,
    })
}

fn parse_price_levels(data: &[u8]) -> Vec<(f64, f64)> {
    let mut result = Vec::new();
    let mut start = 0;
    loop {
        start = match memchr::memchr(b'"', &data[start..]) {
            Some(pos) => pos + start,
            None => break,
        };
        let end = match memchr::memchr(b'"', &data[start + 1..]) {
            Some(pos) => pos + start + 1,
            None => break,
        };
        let price_str = unsafe { from_utf8_unchecked(&data[start + 1..end]) };
        let price = price_str.parse().unwrap();
        result.push((price, 0.0));
        start = end + 1;
        let end = match memchr::memchr(b'[', &data[start..]) {
            Some(pos) => pos + start,
            None => break,
        };
        start = end + 1;
    }
    result
}
