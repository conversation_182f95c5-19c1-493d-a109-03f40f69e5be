use memchr;
use std::str;

#[derive(Debug)]
pub struct BookTicker<'a> {
    pub update_id: u64,
    pub symbol: &'a str,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
}

#[derive(Debug, <PERSON>lone)]
pub struct FuturesBookTicker {
    pub event_time: u64,
    pub transaction_time: u64,
    pub update_id: u64,
    pub symbol: String,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
}

// {
//   "e":"bookTicker",			// event type
//   "u":400900217,     		// order book updateId
//   "E": 1568014460893,  		// event time
//   "T": 1568014460891,  		// transaction time
//   "s":"BNBUSDT",     		// symbol
//   "b":"25.35190000", 		// best bid price
//   "B":"31.21000000", 		// best bid qty
//   "a":"25.36520000", 		// best ask price
//   "A":"40.66000000"  		// best ask qty
// }
pub fn parse_futures_bookticker(input: &[u8]) -> Option<FuturesBookTicker> {
    // First, try to find if this is a stream wrapper message
    let data_pattern = b"\"data\":{";
    if let Some(data_start) = memchr::memmem::find(input, data_pattern) {
        // This is a stream wrapper, extract the data portion
        let data_start = data_start + data_pattern.len() - 1; // -1 to include the {
        let data_end = find_matching_brace(&input[data_start..])?;
        let data_section = &input[data_start..data_start + data_end + 1];
        return parse_bookticker_fast(data_section);
    }

    // If no stream wrapper, try to parse directly
    parse_bookticker_fast(input)
}

/// High-performance parsing using optimized string traversal
/// This function is designed for the specific Binance book ticker format
/// and provides the best performance for high-frequency WebSocket messages
#[inline(always)]
fn parse_bookticker_fast(input: &[u8]) -> Option<FuturesBookTicker> {
    // Expected format: {"e":"bookTicker","u":123,"s":"BTCUSDT","b":"123.45","B":"67.89","a":"123.46","A":"67.90","T":1234567890,"E":1234567890}

    // Quick validation - must start with { and contain bookTicker
    if input.len() < 50 || input[0] != b'{' {
        return None;
    }

    // Parse fields in any order for robustness
    let mut event_time = None;
    let mut transaction_time = None;
    let mut update_id = None;
    let mut symbol = None;
    let mut bid_price = None;
    let mut bid_qty = None;
    let mut ask_price = None;
    let mut ask_qty = None;

    // Find event time (E)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"E\":") {
        event_time = Some(value);
    }

    // Find transaction time (T)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"T\":") {
        transaction_time = Some(value);
    }

    // Find update ID (u)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"u\":") {
        update_id = Some(value);
    }

    // Find symbol (s)
    if let Some(value) = find_and_parse_string_anywhere(input, b"\"s\":\"") {
        symbol = Some(value);
    }

    // Find bid price (b)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"b\":\"") {
        bid_price = Some(value);
    }

    // Find bid quantity (B)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"B\":\"") {
        bid_qty = Some(value);
    }

    // Find ask price (a)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"a\":\"") {
        ask_price = Some(value);
    }

    // Find ask quantity (A)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"A\":\"") {
        ask_qty = Some(value);
    }

    // All fields must be present
    if let (
        Some(event_time),
        Some(transaction_time),
        Some(update_id),
        Some(symbol),
        Some(bid_price),
        Some(bid_qty),
        Some(ask_price),
        Some(ask_qty),
    ) = (
        event_time,
        transaction_time,
        update_id,
        symbol,
        bid_price,
        bid_qty,
        ask_price,
        ask_qty,
    ) {
        Some(FuturesBookTicker {
            event_time,
            transaction_time,
            update_id,
            symbol: symbol.to_string(),
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
        })
    } else {
        None
    }
}

#[inline(always)]
fn find_and_parse_u64_anywhere(input: &[u8], pattern: &[u8]) -> Option<u64> {
    let start = memchr::memmem::find(input, pattern)?;
    let start = start + pattern.len();

    // Look for comma first, then closing brace if no comma found
    let end = if let Some(comma_pos) = memchr::memchr(b',', &input[start..]) {
        comma_pos
    } else if let Some(brace_pos) = memchr::memchr(b'}', &input[start..]) {
        brace_pos
    } else {
        return None;
    };

    let num_str = &input[start..start + end];
    let num_str = unsafe { std::str::from_utf8_unchecked(num_str) };
    num_str.parse::<u64>().ok()
}

#[inline(always)]
fn find_and_parse_f64_anywhere(input: &[u8], pattern: &[u8]) -> Option<f64> {
    let start = memchr::memmem::find(input, pattern)?;
    let start = start + pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let num_str = &input[start..start + end];
    let num_str = unsafe { std::str::from_utf8_unchecked(num_str) };
    num_str.parse::<f64>().ok()
}

#[inline(always)]
fn find_and_parse_string_anywhere<'a>(input: &'a [u8], pattern: &[u8]) -> Option<&'a str> {
    let start = memchr::memmem::find(input, pattern)?;
    let start = start + pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let str_data = &input[start..start + end];
    let str_data = unsafe { std::str::from_utf8_unchecked(str_data) };
    Some(str_data)
}

// Helper function to find matching brace
fn find_matching_brace(input: &[u8]) -> Option<usize> {
    let mut brace_count = 0;
    for (i, &byte) in input.iter().enumerate() {
        match byte {
            b'{' => brace_count += 1,
            b'}' => {
                brace_count -= 1;
                if brace_count == 0 {
                    return Some(i);
                }
            }
            _ => {}
        }
    }
    None
}

pub fn parse_bookticker(input: &[u8]) -> Option<BookTicker<'_>> {
    // 0. 查找 "s":"BTCUSDT" 的位置
    let symbol_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];
    // 1. 查找 "s":"BTCUSDT" 的位置
    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];
    // 2. 查找 "b":0.00000000 的位置
    let input = &input[start + end..];
    let bid_price_pattern = b"\"b\":\"";
    let start = memchr::memmem::find(input, bid_price_pattern)?;
    let start = start + bid_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid_price = &input[start..start + end];
    let input = &input[start + end..];
    // 3. 查找 "B":0.00000000 的位置
    let bid_qty_pattern = b"\"B\":\"";
    let start = memchr::memmem::find(input, bid_qty_pattern)?;
    let start = start + bid_qty_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid_qty = &input[start..start + end];
    // 4. 查找 "a":0.00000000 的位置
    let input = &input[start + end..];
    let ask_price_pattern = b"\"a\":\"";
    let start = memchr::memmem::find(input, ask_price_pattern)?;
    let start = start + ask_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask_price = &input[start..start + end];
    let input = &input[start + end..];
    // 5. 查找 "A":0.00000000 的位置
    let ask_qty_pattern = b"\"A\":\"";
    let start = memchr::memmem::find(input, ask_qty_pattern)?;
    let start = start + ask_qty_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask_qty = &input[start..start + end];

    let symbol = unsafe { std::mem::transmute::<&[u8], &str>(symbol) };
    let bid_price: &str = unsafe { std::mem::transmute(bid_price) };
    let ask_price: &str = unsafe { std::mem::transmute(ask_price) };
    let bid_qty: &str = unsafe { std::mem::transmute(bid_qty) };
    let ask_qty: &str = unsafe { std::mem::transmute(ask_qty) };
    let update_id: &str = unsafe { std::mem::transmute(update_id_str) };

    Some(BookTicker {
        update_id: update_id.parse().unwrap(),
        symbol,
        bid_price: bid_price.parse::<f64>().unwrap(),
        bid_qty: bid_qty.parse::<f64>().unwrap(),
        ask_price: ask_price.parse::<f64>().unwrap(),
        ask_qty: ask_qty.parse::<f64>().unwrap(),
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_futures_bookticker_with_stream_wrapper() {
        let input = r#"{"stream":"btcusdt@bookTicker","data":{"e":"bookTicker","u":8354287306900,"s":"BTCUSDT","b":"117711.00","B":"0.918","a":"117711.10","A":"28.244","T":1755321074652,"E":1755321074652}}"#;
        let result = parse_futures_bookticker(input.as_bytes());

        assert!(result.is_some());
        let book_ticker = result.unwrap();
        assert_eq!(book_ticker.symbol, "BTCUSDT");
        assert_eq!(book_ticker.bid_price, 117711.00);
        assert_eq!(book_ticker.bid_qty, 0.918);
        assert_eq!(book_ticker.ask_price, 117711.10);
        assert_eq!(book_ticker.ask_qty, 28.244);
        assert_eq!(book_ticker.update_id, 8354287306900);
        assert_eq!(book_ticker.event_time, 1755321074652);
        assert_eq!(book_ticker.transaction_time, 1755321074652);
    }

    #[test]
    fn test_parse_futures_bookticker_direct() {
        let input = r#"{"e":"bookTicker","u":8354287306900,"s":"BTCUSDT","b":"117711.00","B":"0.918","a":"117711.10","A":"28.244","T":1755321074652,"E":1755321074652}"#;
        let result = parse_futures_bookticker(input.as_bytes());

        assert!(result.is_some());
        let book_ticker = result.unwrap();
        assert_eq!(book_ticker.symbol, "BTCUSDT");
        assert_eq!(book_ticker.bid_price, 117711.00);
        assert_eq!(book_ticker.bid_qty, 0.918);
        assert_eq!(book_ticker.ask_price, 117711.10);
        assert_eq!(book_ticker.ask_qty, 28.244);
        assert_eq!(book_ticker.update_id, 8354287306900);
        assert_eq!(book_ticker.event_time, 1755321074652);
        assert_eq!(book_ticker.transaction_time, 1755321074652);
    }

    #[test]
    fn test_parse_futures_bookticker_user_error_case() {
        // This is the exact error message the user provided
        let input = r#"{"stream":"btcusdt@bookTicker","data":{"e":"bookTicker","u":8354287306900,"s":"BTCUSDT","b":"117711.00","B":"0.918","a":"117711.10","A":"28.244","T":1755321074652,"E":1755321074652}}"#;
        let result = parse_futures_bookticker(input.as_bytes());

        assert!(
            result.is_some(),
            "Should successfully parse the stream wrapper message"
        );
        let book_ticker = result.unwrap();
        assert_eq!(book_ticker.symbol, "BTCUSDT");
        assert_eq!(book_ticker.bid_price, 117711.00);
        assert_eq!(book_ticker.bid_qty, 0.918);
        assert_eq!(book_ticker.ask_price, 117711.10);
        assert_eq!(book_ticker.ask_qty, 28.244);
        assert_eq!(book_ticker.update_id, 8354287306900);
        assert_eq!(book_ticker.event_time, 1755321074652);
        assert_eq!(book_ticker.transaction_time, 1755321074652);
    }

    #[test]
    fn benchmark_parsing_performance() {
        let test_data = r#"{"e":"bookTicker","u":8354287306900,"s":"BTCUSDT","b":"117711.00","B":"0.918","a":"117711.10","A":"28.244","T":1755321074652,"E":1755321074652}"#;
        let bytes = test_data.as_bytes();

        // Warm up
        for _ in 0..1000 {
            let _ = parse_futures_bookticker(bytes);
        }

        // Benchmark fast parsing
        let start = std::time::Instant::now();
        for _ in 0..10000 {
            let _ = parse_futures_bookticker(bytes);
        }
        let fast_duration = start.elapsed();

        println!("Fast parsing 10,000 times: {:?}", fast_duration);
        println!("Average time per parse: {:?}", fast_duration / 10000);

        // Verify the result is correct
        let result = parse_futures_bookticker(bytes);
        assert!(result.is_some());
        let book_ticker = result.unwrap();
        assert_eq!(book_ticker.symbol, "BTCUSDT");
    }
}
