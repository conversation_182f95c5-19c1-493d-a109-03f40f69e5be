pub fn compute_min_hedge(
    price: f64,
    bn_lot_size: f64,
    bn_min_qty: f64,
    bn_min_national: f64,
    gate_lot_size: f64,
    gate_min_national: f64,
    gate_multiplier: f64,
) -> Option<f64> {
    if !(price.is_finite() && price > 0.0) {
        return None;
    }
    if !(binance.step_qty > 0.0 && gate.multiplier > 0.0 && gate.size_step > 0.0) {
        return None;
    }

    // -------- helpers --------
    fn ceil_to_step(x: f64, step: f64) -> f64 {
        if step <= 0.0 {
            return x;
        }
        let k = (x / step).ceil();
        (k * step).max(0.0)
    }
    fn round_to_int(x: f64) -> i128 {
        // Safe rounding to i128 for scaled integers
        (x.round() as i128)
    }
    fn gcd_i128(mut a: i128, mut b: i128) -> i128 {
        if a < 0 {
            a = -a;
        }
        if b < 0 {
            b = -b;
        }
        while b != 0 {
            let r = a % b;
            a = b;
            b = r;
        }
        a
    }
    fn lcm_i128(a: i128, b: i128) -> i128 {
        if a == 0 || b == 0 {
            return 0;
        }
        (a / gcd_i128(a, b)).saturating_mul(b)
    }
    // get decimal precision <= 9 (enough for typical steps)
    fn infer_decimals(step: f64) -> u32 {
        let mut dec = 0u32;
        let mut v = step;
        while dec < 9 && (v.fract() != 0.0) {
            v *= 10.0;
            dec += 1;
            // guard against FP noise
            if (v - v.round()).abs() < 1e-9 {
                break;
            }
        }
        dec
    }
    // Scale LCM on base-unit steps
    let bin_base_unit = binance.step_qty; // base qty granularity on Binance
    let gate_base_unit = gate.multiplier * gate.size_step; // base qty granularity induced by Gate contracts

    if !(bin_base_unit > 0.0 && gate_base_unit > 0.0) {
        return None;
    }

    // 1) Compute each side's MINIMUM BASE quantity after own constraints
    // Binance: must satisfy min_qty and min_notional; then align to step_qty
    let b_min_qty_step = ceil_to_step(binance.min_qty, binance.step_qty);
    let b_min_qty_from_notional = if binance.min_notional > 0.0 {
        ceil_to_step(binance.min_notional / price, binance.step_qty)
    } else {
        0.0
    };
    let binance_min_base = b_min_qty_step
        .max(b_min_qty_from_notional)
        .max(binance.step_qty);

    // Gate: compute minimum contracts, then convert to base by multiplier
    let g_min_size_step = ceil_to_step(gate.min_size, gate.size_step);
    let g_min_size_from_notional = if gate.min_notional > 0.0 {
        let need_contracts = gate.min_notional / (price * gate.multiplier);
        ceil_to_step(need_contracts, gate.size_step)
    } else {
        0.0
    };
    let gate_min_contracts = g_min_size_step
        .max(g_min_size_from_notional)
        .max(gate.size_step);
    let gate_min_base = gate_min_contracts * gate.multiplier;

    // 2) Find the smallest common base quantity >= both minima
    // Convert steps to integer units via scaling to avoid float LCM
    let decimals = infer_decimals(bin_base_unit)
        .max(infer_decimals(gate_base_unit))
        .min(9);
    let scale: i128 = 10i128.pow(decimals);
    let unit_a = round_to_int(bin_base_unit * scale as f64); // Binance unit in scaled ints
    let unit_b = round_to_int(gate_base_unit * scale as f64); // Gate unit in scaled ints
    if unit_a <= 0 || unit_b <= 0 {
        return None;
    }

    let lcm_units = lcm_i128(unit_a, unit_b);
    if lcm_units <= 0 {
        return None;
    }

    // Starting requirement in scaled ints
    let min_a = round_to_int((binance_min_base * scale as f64).ceil());
    let min_b = round_to_int((gate_min_base * scale as f64).ceil());
    let start = min_a.max(min_b);

    // Ceil to multiple of LCM
    let k = (start + lcm_units - 1) / lcm_units;
    let q_int = k.saturating_mul(lcm_units);
    if q_int <= 0 {
        return None;
    }

    let base_qty_common = (q_int as f64) / (scale as f64); // Q

    // 3) Map back to each venue's order fields (already aligned by construction)
    // Binance order quantity (base)
    let binance_qty = base_qty_common;

    // Gate contracts = Q / multiplier, and should be multiple of size_step
    let gate_contracts = base_qty_common / gate.multiplier;

    // Final safety checks for notionals (should pass; re-assert)
    let b_notional = binance_qty * price;
    let g_notional = gate_contracts * gate.multiplier * price;

    if binance.min_notional > 0.0 && b_notional + 1e-9 < binance.min_notional {
        return None;
    }
    if gate.min_notional > 0.0 && g_notional + 1e-9 < gate.min_notional {
        return None;
    }

    Some(HedgeResult {
        base_qty_common,
        binance_qty,
        gate_contracts,
        notional_usdt: base_qty_common * price,
    })
}
