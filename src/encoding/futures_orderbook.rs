#[derive(Debug)]
pub struct FuturesOrderbookSnapshot<'a> {
    pub event_time: u64,
    pub transaction_time: u64,
    pub update_id: u64,
    pub symbol: &'a str,
    pub bids: Vec<(f64, f64)>,
    pub asks: Vec<(f64, f64)>,
}

// 辅助函数：解析深度数组
fn parse_depth_array(input: &[u8]) -> Vec<(f64, f64)> {
    let mut result = Vec::new();

    // 将字节数组转换为字符串
    let input_str = match str::from_utf8(input) {
        Ok(s) => s,
        Err(_) => return result,
    };

    // 查找数组开始和结束位置
    let start = match input_str.find('[') {
        Some(pos) => pos,
        None => return result,
    };
    let end = match input_str.rfind(']') {
        Some(pos) => pos,
        None => return result,
    };
    let array_content = &input_str[start + 1..end];

    // 分割数组元素 - 格式: ["price1","qty1"],["price2","qty2"]
    let elements: Vec<&str> = array_content
        .split("],[")
        .map(|s| s.trim_matches('[').trim_matches(']'))
        .collect();

    for element in elements.iter() {
        let parts: Vec<&str> = element.split(',').collect();
        if parts.len() >= 2 {
            let price = parts[0].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            let qty = parts[1].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            result.push((price, qty));
        }
    }

    result
}

pub fn parse_futures_orderbook_snapshot(input: &[u8]) -> Option<FuturesOrderbookSnapshot<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];

    let transaction_time_pattern = b"\"T\":";
    let start = memchr::memmem::find(input, transaction_time_pattern)?;
    let start = start + transaction_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let transaction_time_str = &input[start..start + end];

    let update_id_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, update_id_pattern)?;
    let start = start + update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];

    let bids_pattern = b"\"b\":[";
    let start = memchr::memmem::find(input, bids_pattern)?;
    let bids_start = start + bids_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut end = bids_start;
    for (i, &byte) in input[bids_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                end = bids_start + i;
                break;
            }
        }
    }
    let bids_section = &input[bids_start..end];

    let asks_pattern = b"\"a\":[";
    let start = memchr::memmem::find(input, asks_pattern)?;
    let asks_start = start + asks_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut end = asks_start;
    for (i, &byte) in input[asks_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                end = asks_start + i;
                break;
            }
        }
    }
    let asks_section = &input[asks_start..end];

    // 解析深度数组
    let bids = parse_depth_array(bids_section);
    let asks = parse_depth_array(asks_section);

    // 转换字符串为数字
    let event_time = unsafe { str::from_utf8_unchecked(event_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let transaction_time = unsafe { str::from_utf8_unchecked(transaction_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let update_id = unsafe { str::from_utf8_unchecked(update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let symbol = unsafe { str::from_utf8_unchecked(symbol) };

    Some(FuturesOrderbookSnapshot {
        event_time,
        transaction_time,
        update_id,
        symbol,
        bids,
        asks,
    })
}

/*
{
  "lastUpdateId": 1027024,
  "E": 1589436922972,   // Message output time
  "T": 1589436922959,   // Transaction time
  "bids": [
    [
      "4.00000000",     // PRICE
      "431.00000000"    // QTY
    ]
  ],
  "asks": [
    [
      "4.00000200",
      "12.00000000"
    ]
  ]
} */
pub fn parse_http_orderbook_snapshot(data: &[u8]) -> Option<FuturesOrderbookSnapshot<'_>> {
    let last_update_id_pattern = b"\"lastUpdateId\":";
    let start = memchr::memmem::find(data, last_update_id_pattern)?;
    let start = start + last_update_id_pattern.len();
    let end = memchr::memchr(b',', &data[start..])?;
    let last_update_id_str = &data[start..start + end];

    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(data, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &data[start..])?;
    let event_time_str = &data[start..start + end];

    let transaction_time_pattern = b"\"T\":";
    let start = memchr::memmem::find(data, transaction_time_pattern)?;
    let start = start + transaction_time_pattern.len();
    let end = memchr::memchr(b',', &data[start..])?;
    let transaction_time_str = &data[start..start + end];

    let bids_pattern = b"\"bids\":[";
    let start = memchr::memmem::find(data, bids_pattern)?;
    let bids_start = start + bids_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut bids_end = bids_start;
    for (i, &byte) in data[bids_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                bids_end = bids_start + i;
                break;
            }
        }
    }
    let bids_section = &data[bids_start..bids_end];

    let asks_pattern = b"\"asks\":[";
    let start = memchr::memmem::find(data, asks_pattern)?;
    let asks_start = start + asks_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut asks_end = asks_start;
    for (i, &byte) in data[asks_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                asks_end = asks_start + i;
                break;
            }
        }
    }
    let asks_section = &data[asks_start..asks_end];

    let bids = parse_depth_array(bids_section);
    let asks = parse_depth_array(asks_section);

    let last_update_id = unsafe { str::from_utf8_unchecked(last_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let event_time = unsafe { str::from_utf8_unchecked(event_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let transaction_time = unsafe { str::from_utf8_unchecked(transaction_time_str) }
        .parse::<u64>()
        .unwrap_or(0);

    Some(FuturesOrderbookSnapshot {
        event_time,
        transaction_time,
        update_id: last_update_id,
        symbol: "",
        bids,
        asks,
    })
}

// {
//   "e": "depthUpdate", // Event type
//   "E": 123456789,     // Event time
//   "T": 123456788,     // Transaction time
//   "s": "BTCUSDT",     // Symbol
//   "U": 157,           // First update ID in event
//   "u": 160,           // Final update ID in event
//   "pu": 149,          // Final update Id in last stream(ie `u` in last stream)
//   "b": [              // Bids to be updated
//     [
//       "0.0024",       // Price level to be updated
//       "10"            // Quantity
//     ]
//   ],
//   "a": [              // Asks to be updated
//     [
//       "0.0026",       // Price level to be updated
//       "100"          // Quantity
//     ]
//   ]
// }
#[derive(Debug, Clone)]
pub struct FuturesOrderbookUpdate {
    pub event_time: u64,
    pub transaction_time: u64,
    pub first_update_id: u64,
    pub final_update_id: u64,
    pub prev_update_id: u64,
    pub bid_updates: Vec<(f64, f64)>,
    pub ask_updates: Vec<(f64, f64)>,
}

// 辅助函数：解析价格档位更新数组
fn parse_price_levels_update(data: &[u8]) -> Vec<(f64, f64)> {
    let mut result = Vec::new();

    // 将字节数组转换为字符串
    let data_str = match std::str::from_utf8(data) {
        Ok(s) => s,
        Err(_) => return result,
    };

    // 查找数组开始和结束位置
    let start = match data_str.find('[') {
        Some(pos) => pos,
        None => return result,
    };
    let end = match data_str.rfind(']') {
        Some(pos) => pos,
        None => return result,
    };
    let array_content = &data_str[start + 1..end];

    // 分割数组元素
    let elements: Vec<&str> = array_content
        .split("],[")
        .map(|s| s.trim_matches('[').trim_matches(']'))
        .collect();

    for element in elements {
        let parts: Vec<&str> = element.split(',').collect();
        if parts.len() >= 2 {
            let price = parts[0].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            let qty = parts[1].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            result.push((price, qty));
        }
    }

    result
}

pub fn parse_futures_orderbook_update(input: &[u8]) -> Option<FuturesOrderbookUpdate> {
    // 解析事件时间
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];

    // 解析交易时间
    let transaction_time_pattern = b"\"T\":";
    let start = memchr::memmem::find(input, transaction_time_pattern)?;
    let start = start + transaction_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let transaction_time_str = &input[start..start + end];

    // 解析第一个更新ID
    let first_update_id_pattern = b"\"U\":";
    let start = memchr::memmem::find(input, first_update_id_pattern)?;
    let start = start + first_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let first_update_id_str = &input[start..start + end];

    // 解析最终更新ID
    let final_update_id_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, final_update_id_pattern)?;
    let start = start + final_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let final_update_id_str = &input[start..start + end];

    // 解析前一个更新ID
    let prev_update_id_pattern = b"\"pu\":";
    let start = memchr::memmem::find(input, prev_update_id_pattern)?;
    let start = start + prev_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let prev_update_id_str = &input[start..start + end];

    // 解析买单更新
    let bids_pattern = b"\"b\":[";
    let bids_start = memchr::memmem::find(input, bids_pattern)?;
    let bids_start = bids_start + bids_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1;
    let mut bids_end = bids_start;
    for (i, &byte) in input[bids_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                bids_end = bids_start + i;
                break;
            }
        }
    }
    let bids_section = &input[bids_start..bids_end];
    let bid_updates = parse_price_levels_update(bids_section);

    // 解析卖单更新
    let asks_pattern = b"\"a\":[";
    let asks_start = memchr::memmem::find(input, asks_pattern)?;
    let asks_start = asks_start + asks_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1;
    let mut asks_end = asks_start;
    for (i, &byte) in input[asks_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                asks_end = asks_start + i;
                break;
            }
        }
    }
    let asks_section = &input[asks_start..asks_end];
    let ask_updates = parse_price_levels_update(asks_section);

    // 转换字符串为数字
    let event_time = unsafe { std::str::from_utf8_unchecked(event_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let transaction_time = unsafe { std::str::from_utf8_unchecked(transaction_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let first_update_id = unsafe { std::str::from_utf8_unchecked(first_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let final_update_id = unsafe { std::str::from_utf8_unchecked(final_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let prev_update_id = unsafe { std::str::from_utf8_unchecked(prev_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);

    Some(FuturesOrderbookUpdate {
        event_time,
        transaction_time,
        first_update_id,
        final_update_id,
        prev_update_id,
        bid_updates,
        ask_updates,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_http_orderbook_snapshot_single() {
        let data = b"{\"lastUpdateId\":1027024,\"E\":1589436922972,\"T\":1589436922959,\"bids\":[[\"4.00000000\",\"431.00000000\"]],\"asks\":[[\"4.00000200\",\"12.00000000\"]]}";
        let snapshot = parse_http_orderbook_snapshot(data).unwrap();

        assert_eq!(snapshot.bids.len(), 1);
        assert_eq!(snapshot.asks.len(), 1);
        assert_eq!(snapshot.bids[0], (4.0, 431.0));
        assert_eq!(snapshot.asks[0], (4.000002, 12.0));
        assert_eq!(snapshot.update_id, 1027024);
        assert_eq!(snapshot.event_time, 1589436922972);
        assert_eq!(snapshot.transaction_time, 1589436922959);
    }

    #[test]
    fn test_parse_http_orderbook_snapshot_multiple() {
        let data = b"{\"lastUpdateId\":1027025,\"E\":1589436922972,\"T\":1589436922959,\"bids\":[[\"4.00000000\",\"431.00000000\"],[\"3.99999000\",\"100.00000000\"],[\"3.99998000\",\"200.50000000\"]],\"asks\":[[\"4.00000200\",\"12.00000000\"],[\"4.00000300\",\"25.00000000\"],[\"4.00000400\",\"50.75000000\"]]}";
        let snapshot = parse_http_orderbook_snapshot(data).unwrap();

        assert_eq!(snapshot.bids.len(), 3);
        assert_eq!(snapshot.asks.len(), 3);

        // 检查bids
        assert_eq!(snapshot.bids[0], (4.0, 431.0));
        assert_eq!(snapshot.bids[1], (3.99999, 100.0));
        assert_eq!(snapshot.bids[2], (3.99998, 200.5));

        // 检查asks
        assert_eq!(snapshot.asks[0], (4.000002, 12.0));
        assert_eq!(snapshot.asks[1], (4.000003, 25.0));
        assert_eq!(snapshot.asks[2], (4.000004, 50.75));

        assert_eq!(snapshot.update_id, 1027025);
    }

    #[test]
    fn test_parse_http_orderbook_snapshot_empty() {
        let data = b"{\"lastUpdateId\":1027026,\"E\":1589436922972,\"T\":1589436922959,\"bids\":[],\"asks\":[]}";
        let snapshot = parse_http_orderbook_snapshot(data).unwrap();

        assert_eq!(snapshot.bids.len(), 0);
        assert_eq!(snapshot.asks.len(), 0);
        assert_eq!(snapshot.update_id, 1027026);
    }

    #[test]
    fn test_parse_http_orderbook_snapshot_large() {
        // 生成1000档数据
        let mut bids_data = Vec::new();
        let mut asks_data = Vec::new();

        for i in 0..1000 {
            let bid_price = 4.0 - (i as f64) * 0.00001;
            let ask_price = 4.0 + (i as f64) * 0.00001;
            let quantity = 100.0 + (i as f64);

            bids_data.push(format!("[\"{:.8}\",\"{:.8}\"]", bid_price, quantity));
            asks_data.push(format!("[\"{:.8}\",\"{:.8}\"]", ask_price, quantity));
        }

        let bids_str = bids_data.join(",");
        let asks_str = asks_data.join(",");

        let data_string = format!(
            "{{\"lastUpdateId\":1027027,\"E\":1589436922972,\"T\":1589436922959,\"bids\":[{}],\"asks\":[{}]}}",
            bids_str, asks_str
        );
        let data = data_string.as_bytes();

        let snapshot = parse_http_orderbook_snapshot(data).unwrap();

        assert_eq!(snapshot.bids.len(), 1000);
        assert_eq!(snapshot.asks.len(), 1000);

        // 检查第一档和最后一档
        assert_eq!(snapshot.bids[0], (4.0, 100.0));
        assert_eq!(snapshot.bids[999], (4.0 - 999.0 * 0.00001, 1099.0));

        assert_eq!(snapshot.asks[0], (4.0, 100.0));
        assert_eq!(snapshot.asks[999], (4.0 + 999.0 * 0.00001, 1099.0));

        assert_eq!(snapshot.update_id, 1027027);

        println!(
            "Successfully parsed {} bids and {} asks",
            snapshot.bids.len(),
            snapshot.asks.len()
        );
    }

    #[test]
    fn test_parse_depth_array_single() {
        let input = b"[\"4.00000000\",\"431.00000000\"]";
        let result = parse_depth_array(input);

        assert_eq!(result.len(), 1);
        assert_eq!(result[0], (4.0, 431.0));
    }

    #[test]
    fn test_parse_depth_array_multiple() {
        let input = b"[\"4.00000000\",\"431.00000000\"],[\"3.99999000\",\"100.00000000\"]";
        let result = parse_depth_array(input);

        assert_eq!(result.len(), 2);
        assert_eq!(result[0], (4.0, 431.0));
        assert_eq!(result[1], (3.99999, 100.0));
    }

    #[test]
    fn test_parse_depth_array_empty() {
        let input = b"";
        let result = parse_depth_array(input);

        assert_eq!(result.len(), 0);
    }

    #[test]
    fn test_parse_depth_array_invalid() {
        let input = b"invalid json";
        let result = parse_depth_array(input);

        assert_eq!(result.len(), 0);
    }
}
