use libc::{
    CPU_SET, CPU_ZERO, SCHED_FIFO, cpu_set_t, pid_t, sched_param, sched_setaffinity,
    sched_setscheduler,
};
use libwebsocket_rs::{engine::arbitrage_runner_futures::run, info, warn};
use std::{env, mem};

unsafe fn bind_to_core(core_id: usize) {
    unsafe {
        let mut set: cpu_set_t = mem::zeroed();
        CPU_ZERO(&mut set);
        CPU_SET(core_id, &mut set);

        let pid: pid_t = 0; // 0 表示当前线程

        let result = sched_setaffinity(pid, mem::size_of::<cpu_set_t>(), &set);

        if result != 0 {
            warn!(
                "Failed to set CPU affinity: {}",
                std::io::Error::last_os_error()
            );
        } else {
            info!("Successfully bound to core {}", core_id);
        }
    }
}

fn print_usage() {
    info!("Usage: arb_futures [OPTIONS]\n");
    info!("Options:\n");
    info!("  --stdout         Enable stdout logging (logs will be printed to console)\n");
    info!("  --help, -h       Show this help message\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut enable_stdout = false; // 默认不启用stdout日志

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--stdout" => {
                enable_stdout = true;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    // 启用stdout日志输出（如果指定了--stdout参数）
    libwebsocket_rs::utils::logger::enable_stdout_logging(enable_stdout);

    unsafe {
        bind_to_core(0);
        let param = sched_param { sched_priority: 99 };
        let pid = std::process::id() as i32;
        let ret = sched_setscheduler(pid, SCHED_FIFO, &param);
        if ret != 0 {
            warn!(
                "Warning: Failed to set SCHED_FIFO: {}",
                std::io::Error::last_os_error()
            );
            warn!("running as normal priority...");
        }
    }

    run()?;

    Ok(())
}
