use libwebsocket_rs::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    encoding::{
        book_ticker::parse_futures_bookticker,
        futures_order::{
            OrderSide, generate_futures_market_order_request,
            generate_futures_user_data_ping_request,
        },
        futures_order_response::{
            FuturesOrderResponse, OrderStatus, UserDataResponse, parse_futures_order_response,
            parse_user_data_response,
        },
        gate::{self, OrderPlaceMsg, UnifiedOrderStatus},
    },
    engine::{
        arbitrage_engine_gate::ArbitrageGate,
        binance::{generate_futures_book_ticker_url, generate_futures_order_url},
        gate_const::{GATE_BASE_ASSETS, GateBaseAssetCurrency},
        token::{
            ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, USER_DATA_STREAM_1, WS_BBO_1, WS_BBO_2,
        },
        trade::{
            generate_futures_session_logon_request, generate_futures_user_data_start_request,
            generate_futures_user_data_stream_url,
        },
    },
    error, flush_logs, info,
    net::utils::url::Url,
    utils::perf::{system_now_in_secs, system_now_in_us},
};
use std::env;

const GATE_WS_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";
const IN_LEN: usize = 1024 * 32;
const OUT_LEN: usize = 1024 * 4;

fn generate_bbo_url() -> String {
    GATE_WS_URL.to_string()
}

fn print_usage() {
    info!("Usage: gate [OPTIONS]\n");
    info!("Options:\n");
    info!("  --stdout         Enable stdout logging (logs will be printed to console)\n");
    info!("  --help, -h       Show this help message\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut enable_stdout = true; // 默认不启用stdout日志

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--stdout" => {
                enable_stdout = true;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    // 启用stdout日志输出（如果指定了--stdout参数）
    libwebsocket_rs::utils::logger::enable_stdout_logging(enable_stdout);
    let mut engine = ArbitrageGate::new();
    let mut last_ping_time_in_secs = system_now_in_secs();
    let mut listen_key_created = false;
    let mut last_listen_key_ping_time = system_now_in_us();
    let callback = move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    WS_BBO_1 => match gate::decode_bbo(data.as_ref()) {
                        Some(bt) => {
                            if let Some(hedge_order) = engine.update_gate_bbo(&bt) {
                                info!("hedge order: {:?}", hedge_order);
                                let currency =
                                    GateBaseAssetCurrency::from_symbol(&bt.symbol).unwrap();
                                let symbol = currency.to_bn_symbol();
                                handle.send_message(
                                    ORDER_TOKEN_3,
                                    generate_futures_market_order_request(
                                        OrderSide::Sell,
                                        hedge_order.qty,
                                        symbol,
                                        hedge_order.order_id,
                                    ),
                                )?;
                            }
                        }
                        None => {
                            libwebsocket_rs::debug!(
                                "failed to parse gate bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    },
                    WS_BBO_2 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            if let Some(order) = engine.update_bn_bbo(&bt) {
                                let currency =
                                    GateBaseAssetCurrency::from_symbol(&bt.symbol).unwrap();
                                let symbol = currency.to_gate_symbol();
                                if order.status == UnifiedOrderStatus::New {
                                    let order_req = gate::place_order(
                                        order.req_id,
                                        order.price,
                                        OrderSide::Buy,
                                        "poc",
                                        symbol,
                                    );
                                    info!("order place request: {}", order_req);
                                    handle.send_message(ORDER_TOKEN_1, order_req)?;
                                } else {
                                    let order_req = gate::modify_order(
                                        order.req_id,
                                        order.price,
                                        order.order_id,
                                        &bt.symbol,
                                    );
                                    info!("order amend request: {}", order_req);
                                    handle.send_message(ORDER_TOKEN_1, order_req)?;
                                }
                            }
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                        if system_now_in_secs() - last_ping_time_in_secs > 10 {
                            handle.send_message(ORDER_TOKEN_1, gate::generate_ping_req())?;
                            handle.send_message(ORDER_TOKEN_2, gate::generate_ping_req())?;
                            handle.send_message(WS_BBO_1, gate::generate_ping_req())?;
                            last_ping_time_in_secs = system_now_in_secs();
                        }
                        if system_now_in_us() - last_listen_key_ping_time > 300_000_000 {
                            handle.send_message(
                                ORDER_TOKEN_1,
                                generate_futures_user_data_ping_request(),
                            )?;
                            last_listen_key_ping_time = system_now_in_us();
                        }
                    }
                    ORDER_TOKEN_1 => {
                        crate::info!(
                            "recv gate order rsp: {}",
                            String::from_utf8_lossy(data.as_ref())
                        );
                        match gate::parse_order_place(data.as_ref()) {
                            Some(msg) => match msg {
                                OrderPlaceMsg::Ack { .. } => {
                                    crate::info!("order ack: {:?}", msg);
                                }
                                OrderPlaceMsg::Response {
                                    request_id: req_id,
                                    header: _,
                                    order: rsp,
                                } => {
                                    let status = gate::unify_gate_status(
                                        rsp.status.as_ref(),
                                        &rsp.finish_as,
                                        rsp.left,
                                        &rsp.tif,
                                    );
                                    let order_id = rsp.id;
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    info!("order rsp id: {} status: {:?}", rsp.id, status);
                                    if let Some(hedge_order) = engine.update_gate_order_status(
                                        status,
                                        &rsp.contract,
                                        order_id,
                                        req_id,
                                        rsp.price,
                                    ) {
                                        info!("hedge order: {:?}", hedge_order);
                                        let currency =
                                            GateBaseAssetCurrency::from_symbol(&rsp.contract)
                                                .unwrap();
                                        let symbol = currency.to_bn_symbol();
                                        handle.send_message(
                                            ORDER_TOKEN_3,
                                            generate_futures_market_order_request(
                                                OrderSide::Sell,
                                                hedge_order.qty,
                                                symbol,
                                                hedge_order.order_id,
                                            ),
                                        )?;
                                    }
                                }
                                OrderPlaceMsg::Error {
                                    request_id: req_id,
                                    header: _,
                                    err,
                                } => {
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    info!("order place error: {:?}", err);
                                    engine.update_gate_order_status(
                                        UnifiedOrderStatus::Canceled,
                                        "",
                                        0,
                                        req_id,
                                        0.0,
                                    );
                                }
                            },
                            None => {
                                libwebsocket_rs::debug!(
                                    "failed to parse order place response: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                    }
                    ORDER_TOKEN_2 => {
                        if let Some(orders) = gate::parse_order_update(data.as_ref()) {
                            for order in orders {
                                let status = gate::unify_gate_status(
                                    order.status.as_ref(),
                                    &order.finish_as,
                                    order.left,
                                    &order.tif,
                                );
                                info!("order update: {:?}, {:?}", order.text, status);
                                // if let Some(result) = engine.update_order_status(status) {
                                //     handle.send_message(ORDER_TOKEN_1, order_req)?;
                                // }
                            }
                        } else {
                            libwebsocket_rs::debug!(
                                "order sub response: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                            crate::flush_logs!();
                        }
                    }
                    ORDER_TOKEN_3 => {
                        crate::info!(
                            "bn order response: {}",
                            String::from_utf8_lossy(data.as_ref())
                        );
                        if !listen_key_created {
                            handle
                                .send_message(token, generate_futures_user_data_start_request())?;
                            listen_key_created = true;
                        }
                        match parse_futures_order_response(data.as_ref()) {
                            Some(FuturesOrderResponse::ListenKey(key)) => {
                                info!("listen key: {}", key);
                                last_listen_key_ping_time = system_now_in_us();
                                flush_logs!();
                                let url = generate_futures_user_data_stream_url(key);
                                handle.connect(url.into(), USER_DATA_STREAM_1, None)?;
                            }
                            Some(FuturesOrderResponse::Error(order_error)) => {
                                if let Some(_id) = order_error.id {
                                    crate::info!("order response error: {}", order_error);
                                }
                                crate::info!("order response error: {}", order_error);
                            }
                            Some(FuturesOrderResponse::OrderResponse(order_id, order_status)) => {
                                if order_status == OrderStatus::Canceled {
                                    crate::info!("order canceled: {}", order_id);
                                }
                            }
                            Some(FuturesOrderResponse::Unkown(_id)) => {
                                crate::info!(
                                    "order response unkown: {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                            None => {
                                crate::info!(
                                    "order response is none: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                    }
                    USER_DATA_STREAM_1 => {
                        if let Some(order_trade_update) = parse_user_data_response(data.as_ref()) {
                            crate::info!("recv user data: {:?}", order_trade_update);
                            match order_trade_update {
                                UserDataResponse::OrderTradeUpdate(order_trade_update) => {
                                    if order_trade_update.order_status == OrderStatus::Filled {
                                        engine.update_bn_order_status(
                                            order_trade_update.order_status,
                                            order_trade_update.order_id,
                                            order_trade_update.avg_price,
                                            order_trade_update.quantity,
                                        );
                                        crate::info!("order filled: {:?}", order_trade_update);
                                    }
                                    if order_trade_update.order_status != OrderStatus::New
                                        && order_trade_update.order_status
                                            != OrderStatus::PartiallyFilled
                                    {
                                        crate::info!("recv user data: {:?}", order_trade_update);
                                    }
                                }
                            }
                        } else {
                            crate::info!(
                                "recv unknown user data: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    _ => (),
                },
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                WS_BBO_1 => {
                    let req = gate::generate_bbo_subscribe_request();
                    info!("gate bbo subscribe request: {}", req);
                    handle.send_message(WS_BBO_1, req)?;
                }
                WS_BBO_2 => {
                    info!("bn bbo connected");
                }
                ORDER_TOKEN_1 => {
                    info!("gate order connection opened {:?}", token);
                    let req = gate::generate_login_request();
                    info!("login request: {}", req);
                    crate::flush_logs!();
                    handle.send_message(ORDER_TOKEN_1, gate::generate_login_request())?;
                }
                ORDER_TOKEN_2 => {
                    let req = gate::generate_order_sub_request();
                    info!("order sub request: {}", req);
                    crate::flush_logs!();
                    handle.send_message(ORDER_TOKEN_2, req)?;
                }
                ORDER_TOKEN_3 => {
                    info!("Order connection opened: {:?}", token);
                    let req = generate_futures_session_logon_request();
                    handle.send_message(token, req)?;
                }
                USER_DATA_STREAM_1 => {
                    info!("user data stream connected");
                    flush_logs!();
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(100));
    let mut websocket = WebSocket::new(settings, callback)?;

    let gate_bbo_url: Url = generate_bbo_url().into();
    info!("sbe bbo url: {}", gate_bbo_url);
    websocket.connect(gate_bbo_url.clone(), WS_BBO_1)?;

    let bn_bbo_url: Url = generate_futures_book_ticker_url(GATE_BASE_ASSETS).into();
    info!("bbo url: {}", bn_bbo_url);
    websocket.connect(bn_bbo_url.clone(), WS_BBO_2)?;

    let gate_order_url: Url = generate_bbo_url().into();
    info!("order url: {}", gate_order_url);
    websocket.connect(gate_order_url.clone(), ORDER_TOKEN_1)?;
    websocket.connect(gate_order_url.clone(), ORDER_TOKEN_2)?;

    let order_url: Url = generate_futures_order_url().into();
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), ORDER_TOKEN_3)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Websocket run error: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }
    Ok(())
}
