use std::borrow::Cow;
use std::fmt::Debug;
use std::str::from_utf8;

use crate::net::result::{Error, Kind, Result};
use crate::net::utils;
use crate::net::utils::circular_buffer::CircularBuffer;
use crate::net::utils::url::Url;

use httparse::EMPTY_HEADER;

const MAX_HEADERS: usize = 32;

#[derive(Clone, Debug)]
struct Header<'m> {
    name: Cow<'m, str>,
    value: Cow<'m, str>,
}

#[derive(Clone, Debug)]
pub struct HeaderMap<'m> {
    headers: Vec<Header<'m>>,
}

impl<'m> HeaderMap<'m> {
    pub fn new() -> Self {
        HeaderMap {
            headers: Vec::new(),
        }
    }

    pub fn add<N, V>(&mut self, name: N, value: V)
    where
        N: Into<Cow<'m, str>>,
        V: Into<Cow<'m, str>>,
    {
        self.headers.push(Header {
            name: name.into(),
            value: value.into(),
        });
    }

    pub fn iter(&self) -> impl Iterator<Item = (&str, &str)> {
        self.headers
            .iter()
            .map(|h| (h.name.as_ref(), h.value.as_ref()))
    }

    pub fn get_content_length(&self) -> Option<usize> {
        self.headers
            .iter()
            .find(|h| h.name.to_lowercase() == "content-length")
            .map(|h| h.value.parse::<usize>().unwrap())
    }
}

#[derive(Debug, PartialEq, Clone)]
pub enum Method {
    GET,
    POST,
    DELETE,
}

impl Method {
    fn as_str(&self) -> &str {
        match self {
            Method::GET => "GET",
            Method::POST => "POST",
            Method::DELETE => "DELETE",
        }
    }
}

#[derive(Debug, Clone)]
pub struct HttpRequest<'a> {
    pub method: Method,
    pub headers: HeaderMap<'a>,
    pub uri: &'a str,
}

impl HttpRequest<'_> {
    fn new() -> Self {
        HttpRequest {
            method: Method::GET,
            headers: HeaderMap::new(),
            uri: "",
        }
    }

    pub fn format<const N: usize>(&self, w: &mut CircularBuffer<N>) -> Result<()> {
        w.put(self.method.as_str().as_bytes()).unwrap();
        w.put(&b" "[..]).unwrap();
        if self.uri.is_empty() {
            w.put(&b"/"[..]).unwrap();
        } else {
            w.put(self.uri.as_bytes()).unwrap();
        }
        w.put(&b" HTTP/1.1\r\n"[..]).unwrap();
        for header in &self.headers.headers {
            w.put(header.name.as_bytes()).unwrap();
            w.put(&b": "[..]).unwrap();
            w.put(header.value.as_bytes()).unwrap();
            w.put(&b"\r\n"[..]).unwrap();
        }
        w.put(&b"\r\n"[..]).unwrap();
        Ok(())
    }
}

impl<'a> From<&'a str> for HttpRequest<'a> {
    fn from(value: &'a str) -> Self {
        HttpRequest {
            method: Method::GET,
            headers: HeaderMap::new(),
            uri: value,
        }
    }
}

pub trait IntoWebsocketHandshakeRequest<'a> {
    fn into_ws_handshake_request(self) -> Result<HttpRequest<'a>>;
}

impl<'a> IntoWebsocketHandshakeRequest<'a> for &'a Url {
    fn into_ws_handshake_request(self) -> Result<HttpRequest<'a>> {
        let mut request = HttpRequest::new();
        request.method = Method::GET;
        request.uri = &self.path;
        let host = format!("{}:{}", self.host, self.port);
        request.headers.add("Host", host);
        request.headers.add("Connection", "Upgrade");
        request.headers.add("Sec-WebSocket-Version", "13");
        request
            .headers
            .add("Sec-WebSocket-Key", utils::generate_key());
        request.headers.add("Upgrade", "websocket");
        Ok(request)
    }
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub struct StatusCode(pub u16);

impl StatusCode {
    pub const fn new(code: u16) -> Self {
        StatusCode(code)
    }

    pub const SWITCHING_PROTOCOLS: StatusCode = StatusCode::new(101);
    pub const OK: StatusCode = StatusCode::new(200);
    pub const BAD_REQUEST: StatusCode = StatusCode::new(400);
    pub const NOT_FOUND: StatusCode = StatusCode::new(404);
    pub const INTERNAL_SERVER_ERROR: StatusCode = StatusCode::new(500);
}

#[derive(Debug, Clone, PartialEq)]
pub enum HttpBodySpec {
    None,
    Chunked(Option<usize>),
    ContentLength(usize),
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum HttpResponseParseStatus {
    Uninitialized,
    Incomplete,
    Complete,
}

pub struct HttpResponse<'m> {
    pub status: Option<StatusCode>,
    pub headers: Option<HeaderMap<'m>>,
    pub body: Option<Cow<'m, [u8]>>,
    body_spec: HttpBodySpec,
    parse_status: HttpResponseParseStatus,
}

impl Default for HttpResponse<'_> {
    fn default() -> Self {
        HttpResponse::new()
    }
}

impl Debug for HttpResponse<'_> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("HttpResponse")
            .field("status", &self.status)
            .field("headers", &self.headers)
            .field(
                "body",
                &String::from_utf8_lossy(self.body.as_ref().unwrap_or(&Cow::Borrowed(b""))),
            )
            .field("body_spec", &self.body_spec)
            .finish()
    }
}

impl<'a> ToOwned for HttpResponse<'a> {
    type Owned = HttpResponse<'a>;

    fn to_owned(&self) -> Self::Owned {
        HttpResponse {
            status: self.status,
            headers: self.headers.clone(),
            body: self.body.clone(),
            body_spec: self.body_spec.clone(),
            parse_status: self.parse_status,
        }
    }
}

impl HttpResponse<'_> {
    pub fn new() -> Self {
        HttpResponse {
            status: None,
            headers: None,
            body: None,
            body_spec: HttpBodySpec::None,
            parse_status: HttpResponseParseStatus::Uninitialized,
        }
    }

    pub fn is_incomplete(&self) -> bool {
        self.parse_status == HttpResponseParseStatus::Incomplete
    }

    fn parse_headers(&mut self, buffer: &[u8]) -> Result<usize> {
        let mut headers = [EMPTY_HEADER; MAX_HEADERS];
        let mut res = httparse::Response::new(&mut headers);
        let len = match res.parse(buffer)? {
            httparse::Status::Partial => {
                return Err(Error::new(
                    Kind::IncompleteHttpResponse,
                    "Incomplete HTTP response",
                ));
            }
            httparse::Status::Complete(len) => len,
        };
        self.status = Some(StatusCode::new(res.code.unwrap()));
        let mut self_headers = HeaderMap::new();
        for header in &headers {
            let header_name = header.name.to_lowercase();
            let header_value_vec = header.value.to_vec();
            let header_value = String::from_utf8(header_value_vec)?;
            self_headers.add(header_name, header_value);
            if header.name.to_lowercase() == "content-length" {
                self.body_spec = HttpBodySpec::ContentLength(
                    from_utf8(header.value).unwrap().parse::<usize>().unwrap(),
                );
                break;
            }
            if header.name.to_lowercase() == "transfer-encoding" {
                self.body_spec = HttpBodySpec::Chunked(None);
                break;
            }
        }
        self.headers = Some(self_headers);
        Ok(len)
    }

    pub fn parse_from<const N: usize>(&mut self, buffer: &mut CircularBuffer<N>) -> Result<()> {
        self.parse_status = HttpResponseParseStatus::Incomplete;
        let (first, second) = buffer.as_slices();
        let total_slice: Cow<'_, [u8]> = if second.len() > 0 {
            Cow::Owned([first, second].concat())
        } else {
            Cow::Borrowed(first)
        };
        match self.headers {
            Some(_) => match self.body_spec {
                HttpBodySpec::None => {
                    self.parse_status = HttpResponseParseStatus::Complete;
                    Ok(())
                }
                HttpBodySpec::Chunked(None) => {
                    let line_end = total_slice
                        .windows(2)
                        .position(|w| w == b"\r\n")
                        .ok_or_else(|| {
                            Error::new(
                                Kind::IncompleteHttpResponse,
                                "Buffer is too small to parse chunked response",
                            )
                        })?;
                    let size_str = String::from_utf8_lossy(&total_slice[..line_end]);
                    let chunk_size = usize::from_str_radix(&size_str, 16)?;
                    buffer.advance_and_commit_read(line_end + 2);
                    if chunk_size == 0 {
                        self.body_spec = HttpBodySpec::Chunked(None);
                        buffer.advance_and_commit_read(line_end + 2);
                        self.parse_status = HttpResponseParseStatus::Complete;
                        return Ok(());
                    }
                    self.body_spec = HttpBodySpec::Chunked(Some(chunk_size));
                    self.parse_from(buffer)
                }
                HttpBodySpec::Chunked(Some(len)) => {
                    if total_slice.len() >= len + 2 {
                        // +2 for \r\n
                        match self.body {
                            Some(ref mut b) => {
                                b.to_mut().extend_from_slice(&total_slice[..len]);
                            }
                            None => {
                                self.body = Some(Cow::Owned(total_slice[..len].to_vec()));
                            }
                        }
                        self.body_spec = HttpBodySpec::Chunked(None);
                        buffer.advance_and_commit_read(len + 2);
                        self.parse_from(buffer)
                    } else {
                        match self.body {
                            Some(ref mut b) => {
                                b.to_mut()
                                    .extend_from_slice(&total_slice[..total_slice.len()]);
                            }
                            None => {
                                self.body =
                                    Some(Cow::Owned(total_slice[..total_slice.len()].to_vec()));
                            }
                        }
                        let buf_len = total_slice.len();
                        self.body_spec = HttpBodySpec::Chunked(Some(len - buf_len));
                        buffer.advance_and_commit_read(buf_len);
                        Err(Error::new(
                            Kind::IncompleteHttpResponse,
                            "Content length is less than the body length",
                        ))
                    }
                }
                HttpBodySpec::ContentLength(ref mut len) => {
                    if total_slice.len() >= *len {
                        match self.body {
                            Some(ref mut b) => {
                                b.to_mut().extend_from_slice(&total_slice[..*len]);
                            }
                            None => {
                                self.body = Some(Cow::Owned(total_slice[..*len].to_vec()));
                            }
                        }
                        buffer.advance_and_commit_read(*len);
                        self.parse_status = HttpResponseParseStatus::Complete;
                        Ok(())
                    } else {
                        let buf_len = total_slice.len();
                        match self.body {
                            Some(ref mut b) => {
                                b.to_mut().extend_from_slice(&total_slice[..buf_len]);
                            }
                            None => {
                                self.body = Some(Cow::Owned(total_slice[..buf_len].to_vec()));
                            }
                        }
                        *len -= buf_len;
                        buffer.advance_and_commit_read(buf_len);
                        Err(Error::new(
                            Kind::IncompleteHttpResponse,
                            "Content length is less than the body length",
                        ))
                    }
                }
            },
            None => {
                let len = match self.parse_headers(&total_slice) {
                    Ok(len) => len,
                    Err(e) => {
                        self.parse_status = HttpResponseParseStatus::Uninitialized;
                        return Err(e);
                    }
                };
                buffer.advance_and_commit_read(len);
                self.parse_from(buffer)
            }
        }
    }
}

#[cfg(test)]
mod tests {

    use crate::net::{
        message::http::{HeaderMap, HttpResponse},
        utils::{circular_buffer::CircularBuffer, url::Url},
    };

    use super::IntoWebsocketHandshakeRequest;

    #[test]
    fn test_url_to_handshake() {
        let url = Url::from("ws://localhost:8080/some/path?a=b&c=d");
        let req = url.into_ws_handshake_request().unwrap();
        assert_eq!(req.method, super::Method::GET);
        assert_eq!(req.uri, "/some/path?a=b&c=d");
        let url = Url::from("ws://localhost:8080");
        let req = url.into_ws_handshake_request().unwrap();
        assert_eq!(req.method, super::Method::GET);
        assert_eq!(req.uri, "/");
        let url = Url::from("ws://localhost:8080/path");
        let req = url.into_ws_handshake_request().unwrap();
        assert_eq!(req.method, super::Method::GET);
        assert_eq!(req.uri, "/path");
        let url = Url::from("ws://localhost:8080/?a=b");
        let req = url.into_ws_handshake_request().unwrap();
        assert_eq!(req.method, super::Method::GET);
        assert_eq!(req.uri, "/?a=b");
        assert_eq!(req.headers.iter().count(), 5);
        assert_eq!(
            req.headers
                .iter()
                .find(|(k, _)| *k == "Connection")
                .unwrap()
                .1,
            "Upgrade"
        );
        assert_eq!(
            req.headers.iter().find(|(k, _)| *k == "Host").unwrap().1,
            "localhost:8080"
        );
    }

    #[test]
    fn test_request_format() {
        let url = Url::from("ws://localhost:8080/some/path?a=b&c=d");
        let req = url.into_ws_handshake_request().unwrap();
        let mut req_clone = req.clone();
        req_clone.headers = HeaderMap::new();
        for header in req.headers.iter() {
            if header.0 == "Sec-WebSocket-Key" {
                req_clone.headers.add(header.0, "1234567890");
            } else {
                req_clone.headers.add(header.0, header.1);
            }
        }
        let mut buffer = CircularBuffer::<1024>::new();
        req_clone.format(&mut buffer).unwrap();
        let data = buffer.as_slices().0.to_vec();
        let data = std::str::from_utf8(&data).unwrap();
        assert_eq!(
            data,
            "GET /some/path?a=b&c=d HTTP/1.1\r\nHost: localhost:8080\r\nConnection: Upgrade\r\nSec-WebSocket-Version: 13\r\nSec-WebSocket-Key: 1234567890\r\nUpgrade: websocket\r\n\r\n"
        );
    }

    #[test]
    fn test_parse_from() {
        let data = "HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nhello"
            .as_bytes()
            .to_vec();
        let mut buffer = CircularBuffer::<1024>::new();
        let _ = buffer.put(data);
        let mut response = HttpResponse::new();
        response.parse_from(&mut buffer).unwrap();
        assert_eq!(response.is_incomplete(), false);
        assert_eq!(response.status, Some(super::StatusCode::new(200)));
        assert_eq!(response.body.unwrap().to_vec(), "hello".as_bytes().to_vec());
    }

    #[test]
    fn test_parse_from_incomplete() {
        let data = "HTTP/1.1 200 OK".as_bytes();
        let mut buf = CircularBuffer::<1024>::new();
        let _ = buf.put(data);
        let mut response = HttpResponse::new();
        assert!(response.parse_from(&mut buf).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buf.put("\r\nContent-Length: 5\r\n\r\nhello".as_bytes());
        response.parse_from(&mut buf).unwrap();
        assert_eq!(response.is_incomplete(), false);
        assert_eq!(response.status, Some(super::StatusCode::new(200)));
        assert_eq!(
            response.body.unwrap().to_owned(),
            "hello".as_bytes().to_vec()
        );
    }

    #[test]
    fn test_header_incomplete() {
        let mut response = HttpResponse::new();
        let mut buffer = CircularBuffer::<1024>::new();
        let _ = buffer.put(&b"HTTP/1.1 200 OK"[..]);
        assert!(response.parse_from(&mut buffer).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buffer.put(&b"\r\nContent-Length: 5"[..]);
        assert!(response.parse_from(&mut buffer).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buffer.put(&b"\r\nContent-Type: application/json"[..]);
        assert!(response.parse_from(&mut buffer).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buffer.put(&b"\r\n\r\n"[..]);
        assert!(response.parse_from(&mut buffer).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buffer.put(&b"hel"[..]);
        assert!(response.parse_from(&mut buffer).is_err());
        assert_eq!(response.is_incomplete(), true);
        let _ = buffer.put(&b"lo"[..]);
        response.parse_from(&mut buffer).unwrap();
        assert_eq!(response.is_incomplete(), false);
        assert_eq!(response.body.unwrap().to_vec(), "hello".as_bytes().to_vec());
    }
}
