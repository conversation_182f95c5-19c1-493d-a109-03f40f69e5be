{"cells": [{"cell_type": "code", "execution_count": 8, "id": "b813a990", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "pnl = 0\n", "max_pnl = 0\n", "min_pnl = 0\n", "data = pd.read_csv(\"combined_analysis.csv\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "94ca7a62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trigger_symbol</th>\n", "      <th>trigger_log_time</th>\n", "      <th>win_loss</th>\n", "      <th>real_rate</th>\n", "      <th>expect_rate</th>\n", "      <th>pair_symbol</th>\n", "      <th>pair_log_time</th>\n", "      <th>price</th>\n", "      <th>timestamp</th>\n", "      <th>latency</th>\n", "      <th>timestamp_formatted</th>\n", "      <th>log_timestamp_diff_ms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.275582</td>\n", "      <td>Win</td>\n", "      <td>1.000983</td>\n", "      <td>1.000438</td>\n", "      <td>SEIUSDT</td>\n", "      <td>2025-08-05 11:17:35.277821</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>2.342653e+03</td>\n", "      <td>1970-01-01 00:00:00.000</td>\n", "      <td>1.754393e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.275582</td>\n", "      <td>Win</td>\n", "      <td>1.000983</td>\n", "      <td>1.000438</td>\n", "      <td>BTCUSDT</td>\n", "      <td>2025-08-05 11:17:35.278037</td>\n", "      <td>114791.920000</td>\n", "      <td>1754392655276</td>\n", "      <td>2.551700e+03</td>\n", "      <td>1970-01-21 07:19:52.655</td>\n", "      <td>1.752638e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.275582</td>\n", "      <td>Win</td>\n", "      <td>1.000983</td>\n", "      <td>1.000438</td>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.278044</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>2.565887e+03</td>\n", "      <td>1970-01-01 00:00:00.000</td>\n", "      <td>1.754393e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.275582</td>\n", "      <td>Win</td>\n", "      <td>1.000983</td>\n", "      <td>1.000438</td>\n", "      <td>SEIUSDT</td>\n", "      <td>2025-08-05 11:17:35.279511</td>\n", "      <td>0.303900</td>\n", "      <td>1754392655278</td>\n", "      <td>4.032500e+03</td>\n", "      <td>1970-01-21 07:19:52.655</td>\n", "      <td>1.752638e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SEIBTC</td>\n", "      <td>2025-08-05 11:17:35.275582</td>\n", "      <td>Win</td>\n", "      <td>1.000983</td>\n", "      <td>1.000438</td>\n", "      <td>SEIUSDT</td>\n", "      <td>2025-08-05 11:17:35.279590</td>\n", "      <td>0.303900</td>\n", "      <td>1754392655278</td>\n", "      <td>4.111000e+03</td>\n", "      <td>1970-01-21 07:19:52.655</td>\n", "      <td>1.752638e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.953620</td>\n", "      <td>Win</td>\n", "      <td>1.000838</td>\n", "      <td>1.000350</td>\n", "      <td>XRPBTC</td>\n", "      <td>2025-08-08 10:22:26.963301</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>9.722387e+03</td>\n", "      <td>1970-01-01 00:00:00.000</td>\n", "      <td>1.754649e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.953620</td>\n", "      <td>Win</td>\n", "      <td>1.000838</td>\n", "      <td>1.000350</td>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.966593</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>1.301439e+04</td>\n", "      <td>1970-01-01 00:00:00.000</td>\n", "      <td>1.754649e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>274</th>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.953620</td>\n", "      <td>Win</td>\n", "      <td>1.000838</td>\n", "      <td>1.000350</td>\n", "      <td>BTCUSDT</td>\n", "      <td>2025-08-08 10:22:26.967049</td>\n", "      <td>116571.700000</td>\n", "      <td>1754648546956</td>\n", "      <td>1.346990e+04</td>\n", "      <td>1970-01-21 07:24:08.546</td>\n", "      <td>1.752894e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>275</th>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.953620</td>\n", "      <td>Win</td>\n", "      <td>1.000838</td>\n", "      <td>1.000350</td>\n", "      <td>XRPBTC</td>\n", "      <td>2025-08-08 10:22:28.480929</td>\n", "      <td>0.000028</td>\n", "      <td>1754648548480</td>\n", "      <td>1.527342e+06</td>\n", "      <td>1970-01-21 07:24:08.548</td>\n", "      <td>1.752894e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>276</th>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:26.953620</td>\n", "      <td>Win</td>\n", "      <td>1.000838</td>\n", "      <td>1.000350</td>\n", "      <td>XRPUSDT</td>\n", "      <td>2025-08-08 10:22:41.442811</td>\n", "      <td>3.306700</td>\n", "      <td>1754648561441</td>\n", "      <td>1.448917e+07</td>\n", "      <td>1970-01-21 07:24:08.561</td>\n", "      <td>1.752894e+12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>171 rows × 12 columns</p>\n", "</div>"], "text/plain": ["    trigger_symbol            trigger_log_time win_loss  real_rate  \\\n", "0           SEIBTC  2025-08-05 11:17:35.275582      Win   1.000983   \n", "1           SEIBTC  2025-08-05 11:17:35.275582      Win   1.000983   \n", "2           SEIBTC  2025-08-05 11:17:35.275582      Win   1.000983   \n", "3           SEIBTC  2025-08-05 11:17:35.275582      Win   1.000983   \n", "4           SEIBTC  2025-08-05 11:17:35.275582      Win   1.000983   \n", "..             ...                         ...      ...        ...   \n", "272        XRPUSDT  2025-08-08 10:22:26.953620      Win   1.000838   \n", "273        XRPUSDT  2025-08-08 10:22:26.953620      Win   1.000838   \n", "274        XRPUSDT  2025-08-08 10:22:26.953620      Win   1.000838   \n", "275        XRPUSDT  2025-08-08 10:22:26.953620      Win   1.000838   \n", "276        XRPUSDT  2025-08-08 10:22:26.953620      Win   1.000838   \n", "\n", "     expect_rate pair_symbol               pair_log_time          price  \\\n", "0       1.000438     SEIUSDT  2025-08-05 11:17:35.277821       0.000000   \n", "1       1.000438     BTCUSDT  2025-08-05 11:17:35.278037  114791.920000   \n", "2       1.000438      SEIBTC  2025-08-05 11:17:35.278044       0.000000   \n", "3       1.000438     SEIUSDT  2025-08-05 11:17:35.279511       0.303900   \n", "4       1.000438     SEIUSDT  2025-08-05 11:17:35.279590       0.303900   \n", "..           ...         ...                         ...            ...   \n", "272     1.000350      XRPBTC  2025-08-08 10:22:26.963301       0.000000   \n", "273     1.000350     XRPUSDT  2025-08-08 10:22:26.966593       0.000000   \n", "274     1.000350     BTCUSDT  2025-08-08 10:22:26.967049  116571.700000   \n", "275     1.000350      XRPBTC  2025-08-08 10:22:28.480929       0.000028   \n", "276     1.000350     XRPUSDT  2025-08-08 10:22:41.442811       3.306700   \n", "\n", "         timestamp       latency      timestamp_formatted  \\\n", "0                0  2.342653e+03  1970-01-01 00:00:00.000   \n", "1    1754392655276  2.551700e+03  1970-01-21 07:19:52.655   \n", "2                0  2.565887e+03  1970-01-01 00:00:00.000   \n", "3    1754392655278  4.032500e+03  1970-01-21 07:19:52.655   \n", "4    1754392655278  4.111000e+03  1970-01-21 07:19:52.655   \n", "..             ...           ...                      ...   \n", "272              0  9.722387e+03  1970-01-01 00:00:00.000   \n", "273              0  1.301439e+04  1970-01-01 00:00:00.000   \n", "274  1754648546956  1.346990e+04  1970-01-21 07:24:08.546   \n", "275  1754648548480  1.527342e+06  1970-01-21 07:24:08.548   \n", "276  1754648561441  1.448917e+07  1970-01-21 07:24:08.561   \n", "\n", "     log_timestamp_diff_ms  \n", "0             1.754393e+12  \n", "1             1.752638e+12  \n", "2             1.754393e+12  \n", "3             1.752638e+12  \n", "4             1.752638e+12  \n", "..                     ...  \n", "272           1.754649e+12  \n", "273           1.754649e+12  \n", "274           1.752894e+12  \n", "275           1.752894e+12  \n", "276           1.752894e+12  \n", "\n", "[171 rows x 12 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data[\"win_loss\"] == \"Win\"]"]}, {"cell_type": "code", "execution_count": 14, "id": "817bd403", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["win rate avg 0.0004342631578947165\n", "loss rate avg -0.0006101525423729397\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1811258/1251800063.py:2: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  loss_rate_avg = data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['real_rate'] - data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['expect_rate']\n", "/tmp/ipykernel_1811258/1251800063.py:2: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  loss_rate_avg = data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['real_rate'] - data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['expect_rate']\n"]}], "source": ["win_rate_avg = data[data[\"win_loss\"] == \"Win\"][\"real_rate\"] - data[data[\"win_loss\"] == \"Win\"][\"expect_rate\"]\n", "loss_rate_avg = data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['real_rate'] - data[data['win_loss'] == \"Loss\"][data['real_rate'] != 0]['expect_rate']\n", "\n", "print(f\"win rate avg {win_rate_avg.mean()}\")\n", "print(f\"loss rate avg {loss_rate_avg.mean()}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "131b0443", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(154.66666666666666)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data['win_loss'] == \"Win\"]['real_rate'][data['real_rate'] < 1.01].count() / 3"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}