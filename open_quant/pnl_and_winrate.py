#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
tail_and_aggregate.py

语义（满足你的新要求）：
- 同一文件内：Wins/Losses/PNL 是绝对值 => 取“最新值覆盖”
- 同一天内切换到“新日志文件”（新文件计数从 0 开始）：把旧文件的最后值并入当天累计后，从新文件 0 继续累计
- UTC 跨天：清零当天累计；将“当前文件当前值”设为新的基线（不把昨天计入今天）
- 支持进程重启恢复：从 state.json 恢复（当天累计、文件基线/最新值、文件 inode/offset）
- 仅在值变化/文件切换/跨天时写 JSON，并可 scp 到远端

仅依赖标准库 + 系统 scp。
"""

import argparse
import os
import sys
import time
import re
import json
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime, timezone
from typing import Optional, Tuple, Dict, Any

# 解析行
WINS_LOSSES_RE = re.compile(r"Wins:\s*(\d+)\s+Losses:\s*(\d+)")
PNL_RE = re.compile(r"PNL:\s*([+-]?\d+(?:\.\d+)?)")


def utc_now_iso() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ")


def utc_today():
    return datetime.now(timezone.utc).date()


def find_latest_file(log_dir: Path, glob_pattern: str) -> Optional[Path]:
    files = list(log_dir.glob(glob_pattern))
    if not files:
        return None
    files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
    return files[0]


def get_inode(path: Path) -> Optional[int]:
    try:
        return path.stat().st_ino
    except FileNotFoundError:
        return None


def write_json_atomic(path: Path, data: dict) -> None:
    path = path.expanduser().resolve()
    tmp_dir = path.parent
    tmp_dir.mkdir(parents=True, exist_ok=True)
    with tempfile.NamedTemporaryFile("w", delete=False, dir=tmp_dir, encoding="utf-8") as tmp:
        json.dump(data, tmp, ensure_ascii=False, indent=2)
        tmp.flush()
        os.fsync(tmp.fileno())
        tmp_path = Path(tmp.name)
    os.replace(tmp_path, path)


def scp_push(
    local_path: Path, target: str, port: int = 22, identity_file: Optional[str] = None, timeout: int = 15
) -> Tuple[bool, str]:
    local_path = local_path.expanduser().resolve()
    if not local_path.exists():
        return False, f"Local file not found: {local_path}"
    cmd = ["scp", "-P", str(port), "-o", "BatchMode=yes", "-o", "ConnectTimeout=" + str(timeout)]
    if identity_file:
        cmd += ["-i", str(Path(identity_file).expanduser().resolve())]
    cmd += [str(local_path), target]
    try:
        proc = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 5)
        if proc.returncode == 0:
            return True, "OK"
        return False, f"scp failed (code {proc.returncode}): {proc.stderr.strip() or proc.stdout.strip()}"
    except FileNotFoundError:
        return False, "scp command not found. Please install OpenSSH client."
    except subprocess.TimeoutExpired:
        return False, "scp timeout."


# ---------- 状态持久化 ----------
def load_state(path: Optional[Path]) -> Optional[dict]:
    if not path:
        return None
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except Exception as e:
        print(f"[WARN] Failed to load state: {e}", flush=True)
        return None


def save_state(path: Optional[Path], data: dict) -> None:
    if not path:
        return
    try:
        write_json_atomic(path, data)
    except Exception as e:
        print(f"[WARN] Failed to save state: {e}", flush=True)


class TailState:
    """
    聚合逻辑：
    - 当日累计：carry_* 记录“之前所有文件的最终值之和”
    - 当前文件：file_abs_* 记录“当前文件最新绝对值”，file_base_* 记录“当前文件基线”
    - 当日总计 = carry_* + (file_abs_* - file_base_*)；Wins/Losses 用 max(0, ...) 防负数；PNL 可正可负
    - 文件切换：carry_ += (file_abs_ - file_base_)，然后 file_base_* = 0, file_abs_* = 0，从新文件继续
    - UTC 跨天：carry_* = 0，并将 file_base_* = file_abs_*（不把昨天计入今天）
    - 进程重启：
        * 若还是同一文件且未截断：从保存 offset 续读
        * 若已轮转：把旧文件 (abs-base) 并入 carry_*，新文件从 EOF（可回溯 bootstrap_bytes）接上
    """

    def __init__(self, *, start_from: str = "end", bootstrap_bytes: int = 0, state_path: Optional[Path] = None):
        # 文件跟踪
        self.current_file: Optional[Path] = None
        self.current_inode: Optional[int] = None
        self.offset: int = 0
        self.start_from = start_from  # 'end' 或 'begin'
        self.bootstrap_bytes = max(0, bootstrap_bytes)
        self._ever_attached = False

        # 日期 & 累计
        self.day = utc_today()
        self.carry_wins = 0
        self.carry_losses = 0
        self.carry_pnl = 0.0

        # 当前文件绝对值与基线
        self.file_abs_wins = 0
        self.file_abs_losses = 0
        self.file_abs_pnl = 0.0
        self.file_base_wins = 0
        self.file_base_losses = 0
        self.file_base_pnl = 0.0

        # 发射去抖
        self._last_emitted_core: Optional[Dict[str, Any]] = None

        # 状态文件
        self.state_path = state_path

    # ------- 计算当日 totals -------
    def totals(self) -> Tuple[int, int, float]:
        wins = self.carry_wins + max(0, self.file_abs_wins - self.file_base_wins)
        losses = self.carry_losses + max(0, self.file_abs_losses - self.file_base_losses)
        pnl = self.carry_pnl + (self.file_abs_pnl - self.file_base_pnl)
        return wins, losses, pnl

    # ------- 跨天处理 -------
    def maybe_roll_day(self) -> bool:
        today = utc_today()
        if today != self.day:
            # 昨天的当前文件贡献不进入今天：把当前值设为今天的基线
            self.day = today
            self.carry_wins = 0
            self.carry_losses = 0
            self.carry_pnl = 0.0
            self.file_base_wins = self.file_abs_wins
            self.file_base_losses = self.file_abs_losses
            self.file_base_pnl = self.file_abs_pnl
            print(f"[INFO] UTC day changed to {today}. Reset carry and set new file baselines.", flush=True)
            return True
        return False

    # ------- 文件切换 -------
    def _fold_current_file_into_carry(self):
        # 把当前文件的“净贡献”累入 carry
        self.carry_wins += max(0, self.file_abs_wins - self.file_base_wins)
        self.carry_losses += max(0, self.file_abs_losses - self.file_base_losses)
        self.carry_pnl += self.file_abs_pnl - self.file_base_pnl

    def switch_file_if_needed(self, new_file: Optional[Path], *, force_bootstrap=False):
        if new_file is None:
            if self.current_file is not None:
                print("[WARN] No log files found. Waiting...", flush=True)
                self.current_file = None
                self.current_inode = None
                self.offset = 0
            return

        new_inode = get_inode(new_file)
        if new_inode is None:
            return

        if (
            self.current_inode is None
            or new_inode != self.current_inode
            or (self.current_file and new_file != self.current_file)
        ):

            # 先把旧文件净贡献累入 carry（同一天）
            if self.current_file is not None:
                self._fold_current_file_into_carry()

            # 切换
            self.current_file = new_file
            self.current_inode = new_inode

            # 新文件从 EOF（可回溯 bootstrap_bytes）开始
            try:
                size = self.current_file.stat().st_size
            except FileNotFoundError:
                size = 0

            if self.start_from == "begin":
                self.offset = 0
            else:
                if self.bootstrap_bytes > 0 and (force_bootstrap or not self._ever_attached):
                    self.offset = max(size - self.bootstrap_bytes, 0)
                else:
                    self.offset = size

            # 新文件的基线=0，绝对值清零，等待后续行覆盖
            self.file_base_wins = 0
            self.file_base_losses = 0
            self.file_base_pnl = 0.0
            self.file_abs_wins = 0
            self.file_abs_losses = 0
            self.file_abs_pnl = 0.0

            self._ever_attached = True
            print(f"[INFO] Now tailing: {new_file} (inode={new_inode}) offset={self.offset} size={size}", flush=True)

    # ------- 同文件内“引擎重置”检测（极少见：不轮转但计数骤降） -------
    def _maybe_internal_reset(self, new_w: Optional[int], new_l: Optional[int], new_pnl: Optional[float]):
        reset = False
        if new_w is not None and new_w < self.file_abs_wins:
            reset = True
        if new_l is not None and new_l < self.file_abs_losses:
            reset = True
        # 仅 Wins/Losses 降低就判定重置；PNL 允许上下波动

        if reset:
            # 把旧段净贡献先累入 carry，再把基线清零，相当于“段切换”
            self._fold_current_file_into_carry()
            self.file_base_wins = 0
            self.file_base_losses = 0
            self.file_base_pnl = 0.0

    # ------- 增量读取 -------
    def tail_once(self) -> Tuple[bool, int, int, int]:
        """
        返回: (changed, processed_lines, file_size, new_offset)
        """
        changed = False
        processed = 0
        if self.current_file is None:
            return changed, processed, 0, self.offset

        try:
            size = self.current_file.stat().st_size
        except FileNotFoundError:
            self.current_file = None
            self.current_inode = None
            self.offset = 0
            return changed, processed, 0, 0

        # 截断检测
        if size < self.offset:
            print(f"[INFO] Truncated (size {size} < offset {self.offset}). Reset offset to 0.", flush=True)
            self.offset = 0

        if size == self.offset:
            return changed, processed, size, self.offset

        with self.current_file.open("r", encoding="utf-8", errors="replace") as f:
            f.seek(self.offset)
            chunk = f.read(size - self.offset)
            self.offset = f.tell()

        for line in chunk.splitlines():
            processed += 1
            print(line, flush=True)

            # Wins/Losses
            m = WINS_LOSSES_RE.search(line)
            if m:
                new_w = int(m.group(1))
                new_l = int(m.group(2))
                self._maybe_internal_reset(new_w, new_l, None)
                if new_w != self.file_abs_wins or new_l != self.file_abs_losses:
                    self.file_abs_wins, self.file_abs_losses = new_w, new_l
                    changed = True

            # PNL
            mp = PNL_RE.search(line)
            if mp:
                new_p = float(mp.group(1))
                # 不用 PNL 判断重置（Wins/Losses 已处理）；PNL 可上下波动
                if new_p != self.file_abs_pnl:
                    self.file_abs_pnl = new_p
                    changed = True

        # 汇总打印
        tw, tl, tp = self.totals()
        print(f"[SUMMARY][UTC {self.day}] wins={tw} losses={tl} pnl={tp:.10f}", flush=True)
        return changed, processed, size, self.offset

    # ------- 快照 / 是否需要写出 -------
    def core_payload(self) -> Dict[str, Any]:
        tw, tl, tp = self.totals()
        return {
            "utc_date": str(self.day),
            "wins": int(tw),
            "losses": int(tl),
            "pnl": float(tp),
            "file_abs": {"wins": self.file_abs_wins, "losses": self.file_abs_losses, "pnl": self.file_abs_pnl},
            "file_base": {"wins": self.file_base_wins, "losses": self.file_base_losses, "pnl": self.file_base_pnl},
            "carry": {"wins": self.carry_wins, "losses": self.carry_losses, "pnl": self.carry_pnl},
            "source_file": str(self.current_file) if self.current_file else None,
            "source_inode": int(self.current_inode) if self.current_inode is not None else None,
        }

    def snapshot(self) -> dict:
        snap = dict(self.core_payload())
        snap["updated_at"] = utc_now_iso()
        return snap

    def should_emit(self, *, forced: bool = False) -> bool:
        core = self.core_payload()
        if forced:
            self._last_emitted_core = core
            return True
        if self._last_emitted_core != core:
            self._last_emitted_core = core
            return True
        return False

    # ------- 状态持久化 -------
    def to_state_dict(self) -> dict:
        return {
            "utc_date": str(self.day),
            "carry": {"wins": self.carry_wins, "losses": self.carry_losses, "pnl": self.carry_pnl},
            "file_abs": {"wins": self.file_abs_wins, "losses": self.file_abs_losses, "pnl": self.file_abs_pnl},
            "file_base": {"wins": self.file_base_wins, "losses": self.file_base_losses, "pnl": self.file_base_pnl},
            "current_file": str(self.current_file) if self.current_file else None,
            "current_inode": int(self.current_inode) if self.current_inode is not None else None,
            "offset": self.offset,
        }

    def restore_from_state(self, s: dict, latest_file: Optional[Path], *, bootstrap_if_rotated=True):
        # 日期判断
        today = utc_today()
        same_day = str(today) == s.get("utc_date")
        # 先设置当前文件
        self.current_file = latest_file
        self.current_inode = get_inode(latest_file) if latest_file else None
        size = 0
        if latest_file:
            try:
                size = latest_file.stat().st_size
            except FileNotFoundError:
                size = 0

        # 还原“当前文件快照”和“carry”
        if same_day:
            c = s.get("carry", {})
            self.carry_wins = int(c.get("wins", 0))
            self.carry_losses = int(c.get("losses", 0))
            self.carry_pnl = float(c.get("pnl", 0.0))

            fa = s.get("file_abs", {})
            fb = s.get("file_base", {})
            self.file_abs_wins = int(fa.get("wins", 0))
            self.file_abs_losses = int(fa.get("losses", 0))
            self.file_abs_pnl = float(fa.get("pnl", 0.0))
            self.file_base_wins = int(fb.get("wins", 0))
            self.file_base_losses = int(fb.get("losses", 0))
            self.file_base_pnl = float(fb.get("pnl", 0.0))
        else:
            # 跨天：carry 清零；把当前文件的当前值当作新基线
            self.day = today
            fa = s.get("file_abs", {})
            self.carry_wins = self.carry_losses = 0
            self.carry_pnl = 0.0
            self.file_abs_wins = int(fa.get("wins", 0))
            self.file_abs_losses = int(fa.get("losses", 0))
            self.file_abs_pnl = float(fa.get("pnl", 0.0))
            self.file_base_wins = self.file_abs_wins
            self.file_base_losses = self.file_abs_losses
            self.file_base_pnl = self.file_abs_pnl

        # 偏移续读：只有“文件路径+inode 都匹配且未截断”才复用
        prev_path = s.get("current_file")
        prev_inode = s.get("current_inode")
        prev_offset = int(s.get("offset", 0))

        if (
            latest_file
            and prev_path
            and Path(prev_path) == latest_file
            and prev_inode == self.current_inode
            and size >= prev_offset >= 0
        ):
            self.offset = prev_offset
            print(f"[INFO] Restore: same file (inode={self.current_inode}) offset={self.offset}.", flush=True)
        else:
            # 文件已轮转或无法复用 offset：
            # 把“旧文件未并入的贡献”累到 carry，然后新文件从 EOF（可回溯）接上
            if same_day:
                # 把旧文件 (abs-base) 累入 carry
                self._fold_current_file_into_carry()
            # 新文件“清零基线”，等待新内容
            self.file_base_wins = 0
            self.file_base_losses = 0
            self.file_base_pnl = 0.0
            self.file_abs_wins = 0
            self.file_abs_losses = 0
            self.file_abs_pnl = 0.0
            # offset 策略
            if self.start_from == "begin":
                self.offset = 0
            else:
                if self.bootstrap_bytes > 0 and bootstrap_if_rotated:
                    self.offset = max(size - self.bootstrap_bytes, 0)
                else:
                    self.offset = size
            print(f"[INFO] Restore: file rotated or truncated -> new offset={self.offset} size={size}.", flush=True)

        self._ever_attached = True


def main():
    parser = argparse.ArgumentParser(
        description="Tail latest log, aggregate Wins/Losses/PNL per UTC day, write JSON and scp."
    )
    parser.add_argument("--dir", type=str, default=".", help="日志目录")
    parser.add_argument("--glob", type=str, default="*.log", help="文件匹配规则，默认 *.log")
    parser.add_argument("--interval", type=int, default=60, help="轮询间隔秒")
    parser.add_argument("--json-path", type=str, default="./arb_metrics.json", help="本地 JSON 输出路径")
    parser.add_argument(
        "--scp-target",
        type=str,
        default="ubuntu@**************:/home/<USER>/open_quant/win_rate.json",
        help="scp 目标，如 user@host:/path/metrics.json；留空则不传",
    )
    parser.add_argument("--scp-port", type=int, default=22, help="scp 端口，默认 22")
    parser.add_argument("--scp-identity", type=str, default="~/.ssh/id_ed25519", help="scp 私钥路径（可选）")
    parser.add_argument(
        "--state-path", type=str, default="./arb_state.json", help="本地状态文件路径（用于断点续读/当日累计恢复）"
    )
    parser.add_argument("--start-from", choices=["end", "begin"], default="end", help="新接管文件时从尾部/头部开始")
    parser.add_argument(
        "--bootstrap-bytes", type=int, default=65536, help="接管时从文件尾部回溯读取的字节数；0 表示不回溯"
    )
    args = parser.parse_args()

    log_dir = Path(args.dir)
    if not log_dir.exists() or not log_dir.is_dir():
        print(f"[ERROR] Log directory not found or not a directory: {log_dir}", file=sys.stderr)
        sys.exit(1)

    json_path = Path(args.json_path)
    scp_target = args.scp_target.strip()
    scp_port = args.scp_port
    scp_identity = args.scp_identity.strip() or None
    state_path = Path(args.state_path) if args.state_path else None

    state = TailState(start_from=args.start_from, bootstrap_bytes=args.bootstrap_bytes, state_path=state_path)

    print(
        f"[INFO] dir={log_dir} glob={args.glob} interval={args.interval}s start_from={args.start_from} bootstrap={args.bootstrap_bytes}",
        flush=True,
    )
    print(f"[INFO] JSON: {json_path}", flush=True)
    if scp_target:
        print(f"[INFO] SCP: {scp_target} (port {scp_port})", flush=True)
    else:
        print(f"[INFO] SCP disabled.", flush=True)
    print(f"[INFO] STATE: {state_path}", flush=True)

    # 选初始文件并尝试从状态恢复
    latest = find_latest_file(log_dir, args.glob)
    saved = load_state(state_path)
    if saved is not None:
        state.restore_from_state(saved, latest)
    else:
        state.switch_file_if_needed(latest, force_bootstrap=True)

    # 启动时写出一份 JSON（让下游立刻可读）
    snap = state.snapshot()
    write_json_atomic(json_path, snap)
    if scp_target:
        ok, msg = scp_push(json_path, scp_target, port=scp_port, identity_file=scp_identity)
        print(f"[SCP][startup] {('OK' if ok else 'FAIL')} - {msg}", flush=True)
    save_state(state_path, state.to_state_dict())

    try:
        while True:
            rolled = state.maybe_roll_day()

            latest = find_latest_file(log_dir, args.glob)
            state.switch_file_if_needed(latest)

            changed, processed, size, ofs = state.tail_once()
            if processed == 0:
                print(f"[DEBUG] no new bytes (size={size} offset={ofs})", flush=True)
            else:
                print(f"[DEBUG] processed_lines={processed} size={size} new_offset={ofs}", flush=True)

            if state.should_emit(forced=rolled or changed):
                snap = state.snapshot()
                write_json_atomic(json_path, snap)
                print(f"[JSON] wrote {json_path} => {snap}", flush=True)
                if scp_target:
                    ok, msg = scp_push(json_path, scp_target, port=scp_port, identity_file=scp_identity)
                    print(f"[SCP] {('OK' if ok else 'FAIL')} - {msg}", flush=True)
                save_state(state_path, state.to_state_dict())

            time.sleep(args.interval)
    except KeyboardInterrupt:
        print("\n[INFO] Exit requested by user.", flush=True)
        final = state.snapshot()
        write_json_atomic(json_path, final)
        if scp_target:
            ok, msg = scp_push(json_path, scp_target, port=scp_port, identity_file=scp_identity)
            print(f"[SCP][final] {('OK' if ok else 'FAIL')} - {msg}", flush=True)
        save_state(state_path, state.to_state_dict())
        tw, tl, tp = state.totals()
        print(f"[FINAL SUMMARY][UTC {state.day}] wins={tw} losses={tl} pnl={tp:.10f}", flush=True)


if __name__ == "__main__":
    main()
