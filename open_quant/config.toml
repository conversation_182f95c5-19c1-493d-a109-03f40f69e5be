strategy_path = "./strategy.py"
trader_version = "V2"

[web]
is_production = true
secret_id = "T0CtwN0DT9lZjEkT"
secret_key = "f91cade43073ef03351789d6729f06c9"

[[exchanges]]
exchange = "BinanceSwap"
is_colo = false
is_testnet = false
is_unified = false
key = "9HbITeDCxX614QhColLUiJimEa4MLhmCFYmRH6mhjCnY52L1HIOemqVNaXEhbFnX"
passphrase = ""
rebate_rate = 0
secret = "D0vcompTOn2qN8VfW6UeXEMXo3ubtpHBacsyhA9Vh7LVq0xrPedw5nypP4L5pSxo"
use_ws_api = false

[[exchanges]]
exchange = "BinanceSpot"
is_colo = false
is_testnet = false
is_unified = false
key = "9HbITeDCxX614QhColLUiJimEa4MLhmCFYmRH6mhjCnY52L1HIOemqVNaXEhbFnX"
passphrase = ""
rebate_rate = 0
secret = "D0vcompTOn2qN8VfW6UeXEMXo3ubtpHBacsyhA9Vh7LVq0xrPedw5nypP4L5pSxo"
use_ws_api = false
